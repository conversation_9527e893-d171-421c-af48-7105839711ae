export interface ProcessProcedureElement {
    id: string;
    procedure: {
        id: string;
        code: string;
        translate: {
            languageId: number;
            name: string;
        };
        sector: {
            id: string;
            code: string;
            name: {
                languageId: number;
                name: string;
            }
        }
    };
    processDefinition: [
        {
            id: string;
            name: string;
            processingTime: string;
        }
    ];
    startDate: string;
    endDate: string;
    isApplied: number;
    firstTask: {
        id: string;
        candidateGroup: [
          {
            id: string;
            code: string
          }
        ];
        candidateUser: [
          {
            id: string;
            username: [
              {
                value: string
              }
            ]
          }
        ]
    };
    status: number;
}
