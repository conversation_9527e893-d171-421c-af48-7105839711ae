import { Component, OnInit, <PERSON>D<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, LOCALE_ID, AfterViewInit, ViewChild } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { MediaMatcher } from '@angular/cdk/layout';
import { UserService } from 'src/app/data/service/user.service';
import { DomSanitizer, Title } from '@angular/platform-browser';
import { MediaObserver } from '@angular/flex-layout';
import { MainService } from 'src/app/data/service/main/main.service';
import { Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatSidenav } from '@angular/material/sidenav';
import { DeploymentService } from 'src/app/data/service/deployment.service';

@Component({
    selector: 'app-admin-layout-nav',
    templateUrl: './admin-layout-nav.component.html',
    styleUrls: ['./admin-layout-nav.component.scss', '/src/app/app.component.scss']
})
export class AdminLayoutNavComponent implements OnInit, AfterViewInit, OnDestroy {
    config = this.envService.getConfig();
    protected keycloakService: KeycloakService;
    selectedLang: string;
    selectedLangId = 228;
    selectedAgency = '';
    agencyName = '';
    siteName: any;
    isLoggedIn = false;
    userName = '';
    avatar: any;
    linkRemind: any = '';
    homeUrl = null;
    sidebarMenu = [
        {
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Thống kê báo cáo'
                },
                {
                    languageId: 46,
                    name: 'Statistics/Report'
                }
            ],

            icon: 'pie_chart',
            code: 'statistics',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tình trạng giải quyết hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier Resolution Overview'
                        }
                    ],
                    route: 'statistics/dossier-resolution',
                    permission: [
                        {
                            code: 'statisticsAdminMaster'
                        },
                        {
                            code: 'statisticsDossierResolutionOverview'
                        }
                    ]
                },

            ]
        },
        {
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Danh mục báo cáo'
                },
                {
                    languageId: 46,
                    name: 'Report catalog'
                }
            ],

            icon: 'assignment',
            code: 'report-catalog',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản trị phiếu động'
                        },
                        {
                            languageId: 46,
                            name: 'Template management'
                        }
                    ],
                    route: 'template',
                    permission: [
                        {
                            code: 'reporterAdminMaster'
                        },
                        {
                            code: 'reporterTemplateCategory'
                        }
                    ]
                },

            ]
        }
    ];


    listApps = [];
    listOnegateApps = [];

    logoDefaultApp = '';
    position: string;
    UID: string;
    roleUser = [];
    count = 0;
    countSubmenu = 0;
    countAvailableMenu = [];
    countAvailableSubmenu = [];
    countRoute = 0;
    userId: any = '';
    accountId: any = '';
    listDossierRemind: any = [];
    totalDossierRemind: any = 0;
    userPermissions = [];
    pageConfiguration: any;
    notificationEnable = 0;
    listAppEnable = 0;
    siteContent = {
        logoUrl: '',
        faviconUrl: '',
        name: {
            vi: '',
            en: ''
        }
    };
    sidenav = {
        backgroundImg: ''
    };
    footerContent = {
        name: {},
        address: {},
        logoUrl: '',
        right: {
            name: {
            },
            logoUrl: '',
            version: {
            }
        }
    };
    userAgency = [];
    selectedOption: any = '';

    mobileQuery: MediaQueryList;
    private mobileQueryListener: () => void;
    @ViewChild('snav') public snav: MatSidenav;

    constructor(
        keycloakService: KeycloakService,
        changeDetectorRef: ChangeDetectorRef,
        media: MediaMatcher,
        private userService: UserService,
        private mainService: MainService,
        private sanitizer: DomSanitizer,
        private router: Router,
        public mediaObserver: MediaObserver,
        private envService: EnvService,
        private titleService: Title,
        private deploymentService: DeploymentService,
        @Inject(LOCALE_ID) protected localeId: string
    ) {
        this.keycloakService = keycloakService;
        this.mobileQuery = media.matchMedia('(max-width: 600px)');
        this.mobileQueryListener = () => changeDetectorRef.detectChanges();
        // tslint:disable-next-line: deprecation
        this.mobileQuery.addListener(this.mobileQueryListener);
    }

    async ngOnInit(): Promise<void> {
        this.selectedLang = this.localeId;
        this.selectedLangId = Number(localStorage.getItem('languageId'));
        this.selectedAgency = localStorage.getItem('selectedAgencyId');
        this.agencyName = localStorage.getItem('selectedAgencyName');

        this.keycloakService.isLoggedIn().then(r => {
            this.isLoggedIn = r;
        });
        this.setOpenAccordion();
        this.roleUser = this.keycloakService.getUserRoles(true);

        await this.keycloakService.loadUserProfile().then(user => {
            // tslint:disable-next-line: no-string-literal
            this.UID = user['attributes'].user_id;
            this.userService.getUserExperience(this.UID).subscribe(data => {
                if (data.length > 1) {
                    // tslint:disable-next-line:prefer-for-of
                    for (let us = 0; us < data.length; us++) {
                        data[us].agency.nation = this.config.nation;
                    }
                    this.userAgency = data;
                    this.selectedOption = data[0].agency.id;
                    localStorage.setItem('userAgency', JSON.stringify(data[0].agency));
                }
                else {
                    if (data.length > 0) {
                        // tslint:disable-next-line:prefer-for-of
                        for (let us = 0; us < data.length; us++) {
                            data[us].agency.nation = this.config.nation;
                        }
                        this.agencyName = data[0].agency.name;
                        localStorage.setItem('userAgency', JSON.stringify(data[0].agency));
                    }
                }

            });
            // tslint:disable-next-line: no-string-literal
            this.accountId = user['attributes'].account_id[0];
            // tslint:disable-next-line: no-string-literal semicolon
            if (user['attributes'].permissions !== undefined && user['attributes'].permissions !== null) {
                // tslint:disable-next-line: no-string-literal semicolon
                const permissions = JSON.parse(user['attributes'].permissions);
                if (permissions.length > 0) {
                    permissions.forEach(element => {
                        if (element.permission !== undefined && element.permission !== null) {
                            this.userPermissions.push({
                                code: element.permission.code
                            });
                        }
                    });
                }
            }
        });

        await this.keycloakService.loadUserProfile().then(user => {
            // tslint:disable-next-line: no-string-literal
            this.UID = user['attributes'].user_id;
            // tslint:disable-next-line: no-string-literal
            this.accountId = user['attributes'].account_id[0];
            // tslint:disable-next-line: no-string-literal semicolon
            if (user['attributes'].permissions !== undefined && user['attributes'].permissions !== null) {
                // tslint:disable-next-line: no-string-literal semicolon
                const permissions = JSON.parse(user['attributes'].permissions);
                if (permissions.length > 0) {
                    permissions.forEach(element => {
                        if (element.permission !== undefined && element.permission !== null) {
                            this.userPermissions.push({
                                code: element.permission.code
                            });
                        }
                    });
                }
            }
        });

        this.sidebarMenu.forEach((e, index) => {
            e.listSubMenu.forEach(r => {
                Object.assign(r, { isActive: false });
                r.permission.forEach(q => {
                    if (this.userPermissions.filter(uP => uP.code === q.code).length > 0) {
                        this.count = this.count + 1;
                        // tslint:disable-next-line: no-string-literal
                        r['isActive'] = true;
                    }
                });
            });
            this.countAvailableMenu.push(this.count);
            this.count = 0;
        });
        this.getAvatar();
        this.linkRemind = new URL(window.location.href).origin + '/' + localStorage.getItem('language');
        this.mainService.sideNav = this.snav;
        this.getListOnegateApps();
        this.getPageConfiguration();
        this.getListAccountApps();
    }

    ngAfterViewInit() {
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        if (userAgency && userAgency.id !== null) {
            this.siteName = userAgency.name;
        } else {
            this.siteName = JSON.parse(localStorage.getItem('siteName'))[0]['' + this.localeId];
        }
        this.selectedAgency = localStorage.getItem('selectedAgencyId');
        this.agencyName = localStorage.getItem('selectedAgencyName');
        if (this.siteName === null) {
            // tslint:disable-next-line:no-shadowed-variable
            const userAgency = JSON.parse(localStorage.getItem('userAgency'));
            if (userAgency !== null) {
                this.siteName = userAgency.name;
            } else {
                if (this.localeId === 'vi') {
                    this.siteName = this.config.rootAgency.trans.vi.name;
                }
                if (this.localeId === 'en') {
                    this.siteName = this.config.rootAgency.trans.en.name;
                }
            }
        }
        // this.siteName = JSON.parse(localStorage.getItem('siteName'))[0]['' + this.localeId];
        this.selectedAgency = localStorage.getItem('selectedAgencyId');
        this.agencyName = localStorage.getItem('selectedAgencyName');
        this.siteName = '';
        if (this.siteName === '') {
            if (this.localeId === 'vi') {
                this.siteName = this.config.siteTitle.vi;
            }
            if (this.localeId === 'en') {
                this.siteName = this.config.siteTitle.en;
            }
        }
        console.clear();
    }

    login(): void {
        this.keycloakService.login();
    }

    logout(): void {
        localStorage.clear();
        this.keycloakService.logout();
    }

    ngOnDestroy(): void {
        // tslint:disable-next-line: deprecation
        this.mobileQuery.removeListener(this.mobileQueryListener);
    }

    isSidebarActive() {
        if (window.innerWidth < 960) {
            return false;
        } else {
            return true;
        }
    }

    isEnableToolbar() {
        const config = this.deploymentService.getAppDeployment();

        if (!!config) {
            if (config.toolbarResponsiveEnable !== undefined
                && config.toolbarResponsiveEnable !== null
                && config.toolbarResponsiveEnable === 0) {
                if (window.innerWidth < 960) {
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        } else {
            return true;
        }
    }

    async match(permissionOfMenu) {
        if (permissionOfMenu !== undefined && permissionOfMenu !== null && permissionOfMenu.length > 0) {
            permissionOfMenu.forEach(per => {
                if (this.userPermissions.filter(uP => uP.code === per.code).length > 0) {
                    return true;
                }
                else {
                    return false;
                }
            });
        }
    }

    checkAvalableMenu(permissionMenu) {
        return new Promise(resolve => {
            permissionMenu.forEach((pM, index, array) => {
                if (this.userPermissions.filter(uPer => uPer.code === pM.code)) {
                    resolve(true);
                } else {
                    if (index === (array.length - 1)) {
                        resolve(false);
                    }
                }
            });
        });
    }

    matchPosition(position: string, listSubMenu, index) {
        this.countRoute = 0;
        listSubMenu.forEach(r => {
            let currentRoute = r.route;
            if (r.route.lastIndexOf('/') > 0) {
                currentRoute = r.route.substring(0, r.route.lastIndexOf('/'));
            }
            if (currentRoute === position) {
                this.countRoute = this.countRoute + 1;
                this.sidebarMenu.forEach(menu => {
                    menu.active = false;
                });
                this.sidebarMenu[index].active = true;
            }
        });
        if (this.countRoute > 0) {
            return true;
        } else {
            return false;
        }
    }

    setOpenAccordion() {
        const path = this.router.url;
        if (path.lastIndexOf('?') < 0) {
            this.position = path.split('/', 2)[1];
        } else {
            const wtParams = path.split('?')[0];
            this.position = wtParams.split('/', 2)[1];
        }
    }

    getAvatar() {
        const tempUID = localStorage.getItem('tempUID');
        const tempAvatar = localStorage.getItem('tempAvatar');
        const tempUsername = localStorage.getItem('tempUsername');
        if (tempUID === this.UID[0]) {
            this.avatar = tempAvatar;
            this.userName = tempUsername;
        } else {
            this.keycloakService.loadUserProfile().then(user => {
                // tslint:disable-next-line: no-string-literal
                this.userService.getUserInfo(user['attributes'].user_id).subscribe(data => {
                    // tslint:disable-next-line: no-string-literal
                    this.UID = user['attributes'].user_id;
                    localStorage.setItem('tempUID', this.UID[0]);
                    this.userName = data.fullname;
                    localStorage.setItem('tempUsername', this.userName);
                    if (data.avatarId === null) {
                        this.avatar = 'url(' + this.config.cloudStaticURL + 'logo/HCC.png' + ')';
                        localStorage.setItem('tempAvatar', this.avatar);
                    } else {
                        this.userService.getUserAvatar(data.avatarId).subscribe(response => {
                            const dataType = response.type;
                            const binaryData = [];
                            binaryData.push(response);
                            const downloadLink = document.createElement('a');
                            downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
                            document.body.appendChild(downloadLink);
                            this.avatar = this.sanitizer.bypassSecurityTrustStyle('url(' + downloadLink.href + ')');

                            const reader = new FileReader();
                            reader.readAsDataURL(new Blob(binaryData, { type: dataType }));
                            // tslint:disable-next-line:only-arrow-functions
                            reader.onloadend = function() {
                                const base64data = reader.result;
                                localStorage.setItem('tempAvatar', 'url(' + base64data.toString() + ')');
                            };
                        }, err => {
                            this.avatar = 'url(' + this.config.cloudStaticURL + 'logo/HCC.png' + ')';
                            localStorage.setItem('tempAvatar', this.avatar);
                        });
                    }
                }, error => {
                    this.avatar = 'url(' + this.config.cloudStaticURL + 'logo/HCC.png' + ')';
                    localStorage.setItem('tempAvatar', this.avatar);
                });
            });
        }
    }

    getAppsLogo() {
        this.listApps.forEach(app => {
            if (app.logo) {
                if (app.logo.id !== '') {
                    this.mainService.getFile(app.logo.id).subscribe(data => {
                        const reader = new FileReader();
                        reader.addEventListener('load', () => {
                            // tslint:disable-next-line: no-string-literal
                            app.logo['url'] = reader.result;
                        }, false);
                        reader.readAsDataURL(data);
                    }, err => {
                        // tslint:disable-next-line: no-string-literal
                        app.logo['url'] = this.config.cloudStaticURL + 'logo/quoc-huy.png';
                        console.log(err);
                    });
                } else {
                    // tslint:disable-next-line: no-string-literal
                    app.logo['url'] = 'assets/img/quoc-huy.png';
                }
            }
        });
    }

    changeLanguage(lang, id) {
        if (this.localeId !== lang) {
            localStorage.setItem('language', lang);
            localStorage.setItem('languageId', id);
            this.selectedLang = lang;
            const currentUrl = window.location.href;
            if (this.localeId === 'vi') {
                const url = currentUrl.replace('/vi/', '/en/');
                window.open(url, '_self');
            }
            if (this.localeId === 'en') {
                const url = currentUrl.replace('/en/', '/vi/');
                window.open(url, '_self');
            }
        }
    }

    getDossierRemind() {
        this.totalDossierRemind = 0;
        const arrTemp: any = [];
        this.keycloakService.loadUserProfile().then(user => {
            // tslint:disable-next-line: no-string-literal
            const search = '?user-id=' + user['attributes'].user_id;
            this.listDossierRemind = [];
            this.mainService.getDossierRemind(search).subscribe(data => {
                if (data.content.length > 0) {
                    // tslint:disable-next-line:prefer-for-of
                    for (let i = 0; i < data.content.length; i++) {
                        if (arrTemp.length < 1) {
                            arrTemp.push(data.content[i]);
                        } else {
                            let count = 0;
                            // tslint:disable-next-line:prefer-for-of
                            for (let j = 0; j < arrTemp.length; j++) {
                                if (arrTemp[j].id === data.content[i].id) {
                                    arrTemp[j].count += data.content[i].count;
                                    count++;
                                }
                            }
                            if (count === 0) {
                                arrTemp.push(data.content[i]);
                            }
                        }
                    }
                }
                if (arrTemp.length > 0) {
                    // tslint:disable-next-line:prefer-for-of
                    for (let k = 0; k < arrTemp.length; k++) {
                        this.totalDossierRemind += arrTemp[k].count;
                        // tslint:disable-next-line:no-shadowed-variable
                        this.mainService.getNameDefinationTask(arrTemp[k].id).subscribe(data => {
                            arrTemp[k].name = data.name.name;
                            arrTemp[k].nameId = data.name.id;
                            if (this.listDossierRemind.length < 1) {
                                this.listDossierRemind.push(arrTemp[k]);
                            } else {
                                let temp = 0;
                                // tslint:disable-next-line:prefer-for-of
                                for (let l = 0; l < this.listDossierRemind.length; l++) {
                                    if (arrTemp[k].nameId === this.listDossierRemind[l].nameId) {
                                        this.listDossierRemind[l].count += arrTemp[k].count;
                                        temp++;
                                    }
                                }
                                if (temp === 0) {
                                    this.listDossierRemind.push(arrTemp[k]);
                                }
                            }
                        });
                    }
                }
            });
        });
    }

    getPageConfiguration() {
        const config = this.deploymentService.getAppDeployment();

        if (!!config) {
            if (config.site !== undefined && config.site !== null) {
                this.siteContent = config.site;
                this.titleService.setTitle(config.site.name[this.selectedLang]);
            }
            if (config.sidenav !== undefined && config.sidenav !== null) {
                this.sidenav = config.sidenav;
            }
            // get with domain
            const configCloud = this.deploymentService.getAppDeployment();
            if (configCloud.domain && configCloud.domain.length > 0) {
                // tslint:disable-next-line:max-line-length
                const domain = configCloud.domain.filter(listdomain => ((listdomain.domain.toLowerCase().indexOf(window.location.host) > -1) || (window.location.host.toLowerCase().indexOf(listdomain.domain) > -1)));
                if (domain && domain.length > 0) {
                    if (domain[0].footer) {
                        config.footer = domain[0].footer;
                    }
                    if (domain[0].header) {
                        config.header = domain[0].header;
                    }
                }
            }
            // end with domain
            if (config.header !== undefined && config.header !== null) {
                if (config.header.notificationEnable !== undefined && config.header.notificationEnable !== null) {
                    this.notificationEnable = config.header.notificationEnable;
                    if (this.notificationEnable === 1) {
                        this.getDossierRemind();
                    }
                }
                if (config.header.listAppEnable !== undefined && config.header.listAppEnable !== null) {
                    this.listAppEnable = config.header.listAppEnable;
                }
                if (config.header.homeUrl !== undefined && config.header.homeUrl !== null) {
                    this.homeUrl = config.header.homeUrl;
                }
                if (config.header.logoDefaultApp !== undefined && config.header.logoDefaultApp !== null) {
                    this.logoDefaultApp = config.header.logoDefaultApp;
                }
            }
            if (config.footer !== undefined && config.footer !== null) {
                this.footerContent = config.footer;
            }
        }
    }

    getListOnegateApps() {
        const searchString = '?account-id=' + this.accountId + '&tag-id=' + this.config.deploymentId + '&size=50';
        this.mainService.getListAccountApps(searchString).subscribe(data => {
            for (let i = 0; i < data.numberOfElements; i++) {
                if (data.content[i].app.logoId !== null && data.content[i].app.logoId !== '') {
                    this.mainService.getFile(data.content[i].app.logoId).subscribe(response => {
                        const dataType = response.type;
                        const binaryData = [];
                        binaryData.push(response);

                        const downloadLink = document.createElement('a');
                        downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
                        document.body.appendChild(downloadLink);
                        data.content[i].app.logoURL = this.sanitizer.bypassSecurityTrustStyle('url(' + downloadLink.href + ')');
                        this.listOnegateApps.push(data.content[i]);
                    }, err => {
                        data.content[i].app.logoURL = this.config.cloudStaticURL + 'logo/quoc-huy.png';
                        this.listOnegateApps.push(data.content[i]);
                    });
                } else {
                    data.content[i].app.logoURL = this.config.cloudStaticURL + 'logo/quoc-huy.png';
                    this.listOnegateApps.push(data.content[i]);
                }
            }
            this.listOnegateApps = JSON.parse(JSON.stringify(this.listOnegateApps).replace(/null/g, '""'));
        });
    }


    getListAccountApps() {
        const searchString = '?account-id=' + this.accountId + '&size=500&status=1';
        this.mainService.getListAccountApps(searchString).subscribe(data => {
            for (let i = 0; i < data.numberOfElements; i++) {
                if (data.content[i].app.logoId !== null && data.content[i].app.logoId !== '') {
                    this.mainService.getFile(data.content[i].app.logoId).subscribe(response => {
                        const dataType = response.type;
                        const binaryData = [];
                        binaryData.push(response);

                        const downloadLink = document.createElement('a');
                        downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
                        document.body.appendChild(downloadLink);
                        data.content[i].app.logoURL = this.sanitizer.bypassSecurityTrustStyle('url(' + downloadLink.href + ')');
                        // this.listApps.push(data.content[i]);
                    }, err => {
                        if (this.logoDefaultApp !== null && this.logoDefaultApp !== '') {
                            data.content[i].app.logoURL = 'url(' + this.logoDefaultApp + ')';
                        }
                        else {
                            data.content[i].app.logoURL = 'url(' + this.config.cloudStaticURL + '/logo/default_logo_application.png' + ')';
                        }
                        // this.listApps.push(data.content[i]);
                    });
                } else {
                    if (this.logoDefaultApp !== null && this.logoDefaultApp !== '') {
                        data.content[i].app.logoURL = 'url(' + this.logoDefaultApp + ')';
                    }
                    else {
                        data.content[i].app.logoURL = 'url(' + this.config.cloudStaticURL + '/logo/default_logo_application.png' + ')';
                    }
                    // this.listApps.push(data.content[i]);
                }
            }
            this.listApps = data.content;
            // this.listApps = JSON.parse(JSON.stringify(this.listApps).replace(/null/g, '""'));
        });
    }

    onAgencyChange(agency) {
        this.siteName = agency.name;
        localStorage.setItem('userAgency', JSON.stringify(agency));
    }

    onClickRemind(id) {
        // let routerText = '/dossier/processing';
        // if (this.config.listStatusViewReception.indexOf(id) !== -1) {
        //     routerText = '/dossier/online-reception';
        // } else if (this.config.listStatusViewCancel.indexOf(id) !== -1) {
        //     routerText = '/dossier/cancel';
        // }
        // window.open(this.linkRemind + routerText + '?remindId=' + id, '_self');
        // this.router.navigate([routerText], {
        //     queryParams: {
        //         remindId: id
        //     }
        // });
    }
}
