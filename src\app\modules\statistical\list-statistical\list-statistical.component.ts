import { Component, OnInit, ChangeDetectorRef, AfterViewInit, ViewChild, ViewChildren, QueryList } from '@angular/core';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { Router } from '@angular/router';
import { StatisticalService } from 'src/app/data/service/statistical/statistical.service';
import { EnvService } from 'src/app/core/service/env.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { MatSort } from '@angular/material/sort';
import { Address } from 'cluster';

@Component({
  selector: 'app-list-statistical',
  templateUrl: './list-statistical.component.html',
  styleUrls: ['./list-statistical.component.scss', '../../../app.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class ListStatisticalComponent implements OnInit, AfterViewInit {
  @ViewChild('tabGroup') tabGroup;
  @ViewChild('outerSort', { static: true }) sort: MatSort;
  @ViewChildren('innerSort') innerSort: QueryList<MatSort>;
  @ViewChildren('innerTables') innerTables: QueryList<MatTable<Address>>;

  tabLoadTimes: any;
  typeAgency = [];
  procedure = [];
  agency = [];
  sector = [];
  agencyLevel = [];
  tag = 1;
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;
  ELEMENTDATA1: FormElement[] = [];
  dataSource1: MatTableDataSource<FormElement>;
  ELEMENTDATA2: FormElement[] = [];
  dataSource2: MatTableDataSource<FormElement>;
  ELEMENTDATA3: FormElement[] = [];
  dataSource3: MatTableDataSource<FormElement>;
  ELEMENTDATA4: FormElement[] = [];
  dataSource4: MatTableDataSource<FormElement>;
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  config = this.envService.getConfig();
  pgSizeOptions = this.config.pageSizeOptions;
  config1 = this.envService.getConfig();
  pgSizeOptions1 = this.config1.pageSizeOptions;
  config2 = this.envService.getConfig();
  pgSizeOptions2 = this.config2.pageSizeOptions;
  config3 = this.envService.getConfig();
  pgSizeOptions3 = this.config3.pageSizeOptions;
  config4 = this.envService.getConfig();
  pgSizeOptions4 = this.config4.pageSizeOptions;
  size1 = 10;
  pageIndex1 = 1;
  page1 = 1;
  countResult1 = 0;
  size2 = 10;
  pageIndex2 = 1;
  page2 = 1;
  countResult2 = 0;
  size3 = 10;
  pageIndex3 = 1;
  page3 = 1;
  countResult3 = 0;
  size4 = 10;
  pageIndex4 = 1;
  page4 = 1;
  countResult4 = 0;
  selectedLang = 228;

  sectorVal: any;
  typeAgencyVal: any;
  agencyLevelVal: any;
  agencyVal: any;
  procedureVal: any;

  label01 = '';
  label02 = '';
  label03 = '';
  label04 = '';
  label05 = '';

  toDate = '2020-12-31';
  fromDate = '2020-01-01';
  agencyId = '';

  searchForm = new FormGroup({
    fromDate: new FormControl(''),
    toDate: new FormControl(''),
    sector: new FormControl(''),
    typeAgency: new FormControl(''),
    agencyLevel: new FormControl(''),
    agency: new FormControl(''),
    procedure: new FormControl('')
  });
  expandedElement = null;
  displayedColumns11: string[] = ['agencyLevel'];
  displayedColumns12: string[] = ['agencyLevel2'];
  displayedColumns122: string[] = ['agency'];
  displayedColumns14: string[] = ['sectorLevel'];
  displayedColumns: string[] = ['stt', 'sector', 'sum1', 'acceptedOnl', 'pastDossier', 'acceptedDirect', 'sum2', 'returnedOnTime', 'returnedOvertime', 'sum3', 'unresolvedNoTime', 'unresolvedOvertime', 'sum4', 'resolvedOnTime', 'resolvedOvertime', 'unresolved'];
  displayedColumns1: string[] = ['stt1', 'sector1', 'sum11', 'acceptedOnl1', 'pastDossier1', 'acceptedDirect1', 'sum12', 'returnedOnTime1', 'returnedOvertime1', 'sum13', 'unresolvedNoTime1', 'unresolvedOvertime1', 'sum14', 'resolvedOnTime1', 'resolvedOvertime1', 'unresolved1'];
  displayedColumns2: string[] = ['stt2', 'sector2', 'sum21', 'acceptedOnl2', 'pastDossier2', 'acceptedDirect2', 'sum22', 'returnedOnTime2', 'returnedOvertime2', 'sum23', 'unresolvedNoTime2', 'unresolvedOvertime2', 'sum24', 'resolvedOnTime2', 'resolvedOvertime2', 'unresolved2'];
  displayedColumns3: string[] = ['stt3', 'sector3', 'sum3', 'cause3', 'note3'];
  displayedColumns4: string[] = ['stt4', 'procedure4', 'content4', 'file4'];

  // excel
  slideOpen: any;
  columns: any[];
  excelData = [];
  footerData: any[][] = [];

  agencyNameExcel = '';
  // end excel

  constructor(
    private statisticalService: StatisticalService,
    private dialog: MatDialog,
    private envService: EnvService,
    public datepipe: DatePipe,
    private keycloak: KeycloakService,
    private cdRef: ChangeDetectorRef,
    private exportExcel: ExportExcelService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSource1 = new MatTableDataSource(this.ELEMENTDATA1);
    this.dataSource2 = new MatTableDataSource(this.ELEMENTDATA2);
    this.dataSource3 = new MatTableDataSource(this.ELEMENTDATA3);
    this.dataSource4 = new MatTableDataSource(this.ELEMENTDATA4);
  }

  ngOnInit(): void {
    console.log(this.toDate);
    if (localStorage.getItem('language') === 'vi') {
      this.label01 = 'Mẫu 6a';
      this.label02 = 'Mẫu 6b';
      this.label03 = 'Mẫu 6đ';
      this.label04 = 'Mẫu 6g quá hạn';
      this.label05 = 'Mẫu 6g quy định hành chính';
    } else if (localStorage.getItem('language') === 'en') {
      this.label01 = 'Form 6a';
      this.label02 = 'Form 6b';
      this.label03 = 'Form 6đ';
      this.label04 = 'Form 6g is overdue';
      this.label05 = 'Form 6g administrative regulations';
    }
    this.selectedLang = localStorage.getItem('language') === 'vi' ? 228 : 46;
    this.getListAgency();
    if (this.agencyId === 'underfined') {
      this.agencyId = '';
    }
    const searchString = '?page=0&size=' + this.size + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
      + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
    this.getReportForDossier(searchString, 0);
  }

  onConfirm() {
    const formObj = this.searchForm.getRawValue();
    console.log(formObj);
    if (formObj.agency !== null && formObj.agency !== '') {
      this.agencyId = formObj.agency;
    }
    const searchString = '?page=0&size=' + this.size + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
      + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
    console.log(this.tabGroup.selectedIndex);
    this.getReportForDossier(searchString, (this.tabGroup.selectedIndex ? this.tabGroup.selectedIndex : 0));
  }

  changeAgency($event) {
    const value = $event.target.value;
    this.agencyId = value;
  }

  searchBtn() {
    const formObj = this.searchForm.getRawValue();
    console.log(formObj);
    if (formObj.agency !== null && formObj.agency !== '') {
      this.agencyId = formObj.agency;
    }
    const searchString = '?page=0&size=' + this.size + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
      + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
    console.log(this.tabGroup.selectedIndex);
    this.getReportForDossier(searchString, (this.tabGroup.selecItedndex ? this.tabGroup.selectedIndex : 0));
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  getTabLoaded(tabChangeEvent: MatTabChangeEvent): void {
    const searchString = '?page=0&size=' + this.size + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
      + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
    this.tabGroup.selecItedndex = tabChangeEvent.index;
    this.getReportForDossier(searchString, tabChangeEvent.index);
  }

  toggleRow(element) {
    this.expandedElement = element;
    this.cdRef.detectChanges();
  }

  getReportForDossier(searchString, form = 0) {
    // form 6a
    if (form === 0) {
      this.statisticalService.getReportForDossier6a(searchString).subscribe(data => {
        this.ELEMENTDATA = [];
        const data0 = [];
        let totalReceived = 0;
        let totalReceivedOnl = 0;
        let totalReceivedTT = 0;
        let totalResolvedOverdue = 0;
        let totalResolved = 0;
        let totalResolvedEarly = 0;
        let totalUnresolved = 0;
        let totalUnresolvedHadTime = 0;
        let totalUnresolvedOverdue = 0;
        let sectorName = '';
        let i0 = 0;

        for (const dt of data.content) {
          for (const n of dt.sector.name) {
            if (n.languageId === this.selectedLang) {
              sectorName = n.name;
            }
          }
          totalReceived += dt.received;
          totalReceivedOnl += dt.receivedOnline;
          totalReceivedTT += (totalReceived - totalReceivedOnl);
          totalResolvedOverdue += dt.resolvedOverdue;
          totalResolvedEarly += dt.resolvedEarly;
          totalResolved += (totalResolvedEarly + totalResolvedOverdue);
          // unresolved = (dt.received - totalResolvedEarly - totalResolvedOverdue - dt.cancelled - dt.deleted - dt.suspended);
          totalUnresolved += dt.unresolved;
          totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
          totalUnresolvedOverdue += dt.unresolvedOverdue;
          data0.push({
            stt: i0 + 1,
            sector: sectorName,
            received: dt.received,
            receivedOnline: dt.receivedOnline,
            receivedTT: (dt.received - dt.receivedOnline),
            receivedOld: 0,
            resolved: (dt.resolvedEarly + dt.resolvedOverdue),
            resolvedEarly: dt.resolvedEarly,
            resolvedOverdue: dt.resolvedOverdue,
            unresolvedOverdue: dt.unresolvedOverdue,
            unresolved: dt.unresolved,
            unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
            total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved
          });
          i0++;
        }
        data0.push({
          stt: '',
          sector: 'Tổng cộng',
          received: totalReceived,
          receivedOnline: totalReceivedOnl,
          receivedTT: totalReceivedTT,
          receivedOld: 0,
          resolvedOverdue: totalResolvedOverdue,
          resolvedEarly: totalResolvedEarly,
          resolved: totalResolved,
          unresolved: totalUnresolved,
          unresolvedHadTime: totalUnresolvedHadTime,
          unresolvedOverdue: totalUnresolvedOverdue,
          total: totalResolvedEarly + totalResolvedOverdue + totalUnresolved
        });
        this.countResult = i0;
        this.ELEMENTDATA = data0;
        this.dataSource.data = this.ELEMENTDATA;
      });
    }
    // end form 6a

    // form 6b
    if (form === 1) {
      this.statisticalService.getReportForDossier6b(searchString).subscribe(data => {
        this.ELEMENTDATA1 = [];
        let data1 = [];
        const dataTotal = [];
        let totalReceived = 0;
        let totalReceivedOnl = 0;
        let totalReceivedTT = 0;
        let totalResolvedOverdue = 0;
        let totalResolved = 0;
        let totalResolvedEarly = 0;
        let totalUnresolved = 0;
        let totalUnresolvedHadTime = 0;
        let totalUnresolvedOverdue = 0;
        let sectorName = '';
        let i1 = 1;
        let iTotal1 = 0; // data.numberOfElements;
        let agencyLevelOld = '';
        let agencyLevelName = '';
        // start for
        for (const dt of data.content) {
          if (agencyLevelOld === '' || agencyLevelOld !== dt.agencyLevel.id) {
            if (agencyLevelOld === '') {
              iTotal1++;
              agencyLevelOld = dt.agencyLevel.id;
              for (const n of dt.agencyLevel.name) {
                if (n.languageId === this.selectedLang) {
                  agencyLevelName = n.name;
                }
              }

              for (const n of dt.sector.name) {
                if (n.languageId === this.selectedLang) {
                  sectorName = n.name;
                }
              }
              totalReceived += dt.received;
              totalReceivedOnl += dt.receivedOnline;
              totalReceivedTT += (totalReceived - totalReceivedOnl);
              totalResolvedOverdue += dt.resolvedOverdue;
              totalResolvedEarly += dt.resolvedEarly;
              totalResolved += (totalResolvedEarly + totalResolvedOverdue);
              totalUnresolved += dt.unresolved;
              totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
              totalUnresolvedOverdue += dt.unresolvedOverdue;
              data1.push({
                stt: i1,
                sector: sectorName,
                received: dt.received,
                receivedOnline: dt.receivedOnline,
                receivedTT: (dt.received - dt.receivedOnline),
                receivedOld: 0,
                resolved: (dt.resolvedEarly + dt.resolvedOverdue),
                resolvedEarly: dt.resolvedEarly,
                resolvedOverdue: dt.resolvedOverdue,
                unresolvedOverdue: dt.unresolvedOverdue,
                unresolved: totalUnresolved,
                unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
                total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved
              });
            }

            if (agencyLevelOld !== dt.agencyLevel.id) {
              // set value for array total
              if (localStorage.getItem('language') === 'vi') {
                dataTotal.push({
                  agencyLevel: {
                    id: agencyLevelOld,
                    name: 'TTHC thuộc phạm vi thẩm quyền của UBND ' + agencyLevelName
                  },
                  value: new MatTableDataSource(data1)
                });
              }
              else {
                dataTotal.push({
                  agencyLevel: {
                    id: agencyLevelOld,
                    name: 'Administrative procedures fall under the jurisdiction of the People\'s Committee ' + agencyLevelName
                  },
                  value: new MatTableDataSource(data1)
                });
              }
              // end set value for array total
              i1 = 1;
              iTotal1++;
              data1 = [];
              agencyLevelOld = dt.agencyLevel.id;
              for (const n of dt.agencyLevel.name) {
                if (n.languageId === this.selectedLang) {
                  agencyLevelName = n.name;
                }
              }

              for (const n of dt.sector.name) {
                if (n.languageId === this.selectedLang) {
                  sectorName = n.name;
                }
              }
              totalReceived += dt.received;
              totalReceivedOnl += dt.receivedOnline;
              totalReceivedTT += (totalReceived - totalReceivedOnl);
              totalResolvedOverdue += dt.resolvedOverdue;
              totalResolvedEarly += dt.resolvedEarly;
              totalResolved += (totalResolvedEarly + totalResolvedOverdue);
              totalUnresolved += dt.unresolved;
              totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
              totalUnresolvedOverdue += dt.unresolvedOverdue;
              data1.push({
                stt: i1,
                sector: sectorName,
                received: dt.received,
                receivedOnline: dt.receivedOnline,
                receivedTT: (dt.received - dt.receivedOnline),
                receivedOld: 0,
                resolved: (dt.resolvedEarly + dt.resolvedOverdue),
                resolvedEarly: dt.resolvedEarly,
                resolvedOverdue: dt.resolvedOverdue,
                unresolvedOverdue: dt.unresolvedOverdue,
                unresolved: totalUnresolved,
                unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
                total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved
              });
            }
          } else {
            i1++;
            for (const n of dt.sector.name) {
              if (n.languageId === this.selectedLang) {
                sectorName = n.name;
              }
            }
            totalReceived += dt.received;
            totalReceivedOnl += dt.receivedOnline;
            totalReceivedTT += (totalReceived - totalReceivedOnl);
            totalResolvedOverdue += dt.resolvedOverdue;
            totalResolvedEarly += dt.resolvedEarly;
            totalResolved += (totalResolvedEarly + totalResolvedOverdue);
            totalUnresolved += dt.unresolved;
            totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
            totalUnresolvedOverdue += dt.unresolvedOverdue;
            data1.push({
              stt: i1,
              sector: sectorName,
              received: dt.received,
              receivedOnline: dt.receivedOnline,
              receivedTT: (dt.received - dt.receivedOnline),
              receivedOld: 0,
              resolved: (dt.resolvedEarly + dt.resolvedOverdue),
              resolvedEarly: dt.resolvedEarly,
              resolvedOverdue: dt.resolvedOverdue,
              unresolvedOverdue: dt.unresolvedOverdue,
              unresolved: totalUnresolved,
              unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
              total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved
            });
          }

          if (i1 === data.numberOfElements) {
            if (localStorage.getItem('language') === 'vi') {
              dataTotal.push({
                agencyLevel: {
                  id: agencyLevelOld,
                  name: 'TTHC thuộc phạm vi thẩm quyền của UBND ' + agencyLevelName
                },
                value: new MatTableDataSource(data1)
              });
            }
            else {
              dataTotal.push({
                agencyLevel: {
                  id: agencyLevelOld,
                  name: 'Administrative procedures fall under the jurisdiction of the People\'s Committee ' + agencyLevelName
                },
                value: new MatTableDataSource(data1)
              });
            }
          }
        }
        // end for
        data1 = [];
        data1.push({
          stt: '',
          sector: (localStorage.getItem('language') === 'vi' ? 'Tổng cộng' : 'Total'),
          received: totalReceived,
          receivedOnline: totalReceivedOnl,
          receivedTT: totalReceivedTT,
          receivedOld: 0,
          resolvedOverdue: totalResolvedOverdue,
          resolvedEarly: totalResolvedEarly,
          resolved: totalResolved,
          unresolved: totalUnresolved,
          unresolvedHadTime: totalUnresolvedHadTime,
          unresolvedOverdue: totalUnresolvedOverdue,
          total: totalResolvedEarly + totalResolvedOverdue + totalUnresolved
        });
        dataTotal.push({
          agencyLevel: { id: '', name: (localStorage.getItem('language') === 'vi' ? 'Tổng cộng' : 'Total') },
          value: new MatTableDataSource(data1)
        });
        this.countResult1 = iTotal1 - 1;
        this.ELEMENTDATA1 = dataTotal;
        this.dataSource1.data = this.ELEMENTDATA1;
      });
    }
    // end form 6b

    // form 6d
    if (form === 2) {
      this.statisticalService.getReportForDossier6d(searchString).subscribe(data => {
        this.ELEMENTDATA2 = [];
        let data2 = [];
        const dataTotal2 = [];
        let dataAgency2 = [];
        let totalReceived = 0;
        let totalReceivedOnl = 0;
        let totalReceivedTT = 0;
        let totalResolvedOverdue = 0;
        let totalResolved = 0;
        let totalResolvedEarly = 0;
        let totalUnresolved = 0;
        let totalUnresolvedHadTime = 0;
        let totalUnresolvedOverdue = 0;
        let sectorName = '';
        let i2 = 1;
        let iagency2 = 1;
        let iTotal2 = 0;
        let agencyLevelOld = '';
        let agencyLevelName = '';
        let agencyOld = '';
        let agencyName = '';
        // start for
        for (const dt of data.content) {
          if (agencyLevelOld === '' || agencyLevelOld !== dt.agencyLevel.id) {
            if (agencyLevelOld === '') {
              iTotal2++;
              agencyLevelOld = dt.agencyLevel.id;
              for (const n of dt.agencyLevel.name) {
                if (n.languageId === this.selectedLang) {
                  agencyLevelName = n.name;
                }
              }
              // xet agency
              if (agencyOld === '' || agencyOld !== dt.agency.id) {
                i2 = 1;
                if (agencyOld === '') {
                  for (const n of dt.sector.name) {
                    if (n.languageId === this.selectedLang) {
                      sectorName = n.name;
                    }
                  }
                  for (const n of dt.agency.name) {
                    if (n.languageId === this.selectedLang) {
                      agencyName = n.name;
                    }
                  }
                  totalReceived += dt.received;
                  totalReceivedOnl += dt.receivedOnline;
                  totalReceivedTT += (totalReceived - totalReceivedOnl);
                  totalResolvedOverdue += dt.resolvedOverdue;
                  totalResolvedEarly += dt.resolvedEarly;
                  totalResolved += (totalResolvedEarly + totalResolvedOverdue);
                  totalUnresolved += dt.unresolved;
                  totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
                  totalUnresolvedOverdue += dt.unresolvedOverdue;
                  data2.push({
                    stt: i2,
                    sector: sectorName,
                    received: dt.received,
                    receivedOnline: dt.receivedOnline,
                    receivedTT: (dt.received - dt.receivedOnline),
                    receivedOld: 0,
                    resolved: (dt.resolvedEarly + dt.resolvedOverdue),
                    resolvedEarly: dt.resolvedEarly,
                    resolvedOverdue: dt.resolvedOverdue,
                    unresolvedOverdue: dt.unresolvedOverdue,
                    unresolved: totalUnresolved,
                    unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
                    total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved
                  });
                }
                if (agencyOld !== dt.agency.id) {
                  // set value for array total
                  if (localStorage.getItem('language') === 'vi') {
                    dataAgency2.push({
                      agency: iagency2 + '. TTHCC do ' + agencyName + ' tiếp nhận, giải quyết TTHC',
                      value: data2
                    });
                  }
                  else {
                    dataAgency2.push({
                      agency: iagency2 + '. Public administrative procedures for ' + agencyName + ' receive and settle administrative procedures',
                      value: data2
                    });
                  }
                  // end set value for array total
                  i2 = 1;
                  iagency2++;
                  data2 = [];
                  agencyOld = dt.agency.id;
                  for (const n of dt.agency.name) {
                    if (n.languageId === this.selectedLang) {
                      agencyName = n.name;
                    }
                  }

                  for (const n of dt.sector.name) {
                    if (n.languageId === this.selectedLang) {
                      sectorName = n.name;
                    }
                  }
                  totalReceived += dt.received;
                  totalReceivedOnl += dt.receivedOnline;
                  totalReceivedTT += (totalReceived - totalReceivedOnl);
                  totalResolvedOverdue += dt.resolvedOverdue;
                  totalResolvedEarly += dt.resolvedEarly;
                  totalResolved += (totalResolvedEarly + totalResolvedOverdue);
                  totalUnresolved += dt.unresolved;
                  totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
                  totalUnresolvedOverdue += dt.unresolvedOverdue;
                  data2.push({
                    stt: i2,
                    sector: sectorName,
                    received: dt.received,
                    receivedOnline: dt.receivedOnline,
                    receivedTT: (dt.received - dt.receivedOnline),
                    receivedOld: 0,
                    resolved: (dt.resolvedEarly + dt.resolvedOverdue),
                    resolvedEarly: dt.resolvedEarly,
                    resolvedOverdue: dt.resolvedOverdue,
                    unresolvedOverdue: dt.unresolvedOverdue,
                    unresolved: totalUnresolved,
                    unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
                    total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved
                  });
                }
              }else {
                i2++;
                for (const n of dt.sector.name) {
                  if (n.languageId === this.selectedLang) {
                    sectorName = n.name;
                  }
                }
                totalReceived += dt.received;
                totalReceivedOnl += dt.receivedOnline;
                totalReceivedTT += (totalReceived - totalReceivedOnl);
                totalResolvedOverdue += dt.resolvedOverdue;
                totalResolvedEarly += dt.resolvedEarly;
                totalResolved += (totalResolvedEarly + totalResolvedOverdue);
                totalUnresolved += dt.unresolved;
                totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
                totalUnresolvedOverdue += dt.unresolvedOverdue;
                data2.push({
                  stt: i2,
                  sector: sectorName,
                  received: dt.received,
                  receivedOnline: dt.receivedOnline,
                  receivedTT: (dt.received - dt.receivedOnline),
                  receivedOld: 0,
                  resolved: (dt.resolvedEarly + dt.resolvedOverdue),
                  resolvedEarly: dt.resolvedEarly,
                  resolvedOverdue: dt.resolvedOverdue,
                  unresolvedOverdue: dt.unresolvedOverdue,
                  unresolved: totalUnresolved,
                  unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
                  total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved
                });
              }
              if (i2 === data.numberOfElements) {
                if (localStorage.getItem('language') === 'vi') {
                  dataTotal2.push({
                    agencyLevel: {
                      id: agencyLevelOld,
                      name: 'TTHC thuộc phạm vi thẩm quyền của UBND ' + agencyLevelName
                    },
                    value: new MatTableDataSource(dataAgency2)
                  });
                }
                else {
                  dataTotal2.push({
                    agencyLevel: {
                      id: agencyLevelOld,
                      name: 'Administrative procedures fall under the jurisdiction of the People\'s Committee ' + agencyLevelName
                    },
                    value: new MatTableDataSource(dataAgency2)
                  });
                }
              }
              // end xet agency
            }

            if (agencyLevelOld !== dt.agencyLevel.id) {
              // set value for array total
              if (localStorage.getItem('language') === 'vi') {
                dataTotal2.push({
                  agencyLevel: {
                    id: agencyLevelOld,
                    name: 'TTHC thuộc phạm vi thẩm quyền của UBND ' + agencyLevelName
                  },
                  value: new MatTableDataSource(dataAgency2)
                });
              }
              else {
                dataTotal2.push({
                  agencyLevel: {
                    id: agencyLevelOld,
                    name: 'Administrative procedures fall under the jurisdiction of the People\'s Committee ' + agencyLevelName
                  },
                  value: new MatTableDataSource(dataAgency2)
                });
              }
              // end set value for array total
              iTotal2++;
              agencyLevelOld = dt.agencyLevel.id;
              for (const n of dt.agencyLevel.name) {
                if (n.languageId === this.selectedLang) {
                  agencyLevelName = n.name;
                }
              }
            }
          } else {
            // xet agency
            if (agencyOld === '' || agencyOld !== dt.agency.id) {
              i2 = 1;
              if (agencyOld === '') {
                for (const n of dt.sector.name) {
                  if (n.languageId === this.selectedLang) {
                    sectorName = n.name;
                  }
                }
                for (const n of dt.agency.name) {
                  if (n.languageId === this.selectedLang) {
                    agencyName = n.name;
                  }
                }
                totalReceived += dt.received;
                totalReceivedOnl += dt.receivedOnline;
                totalReceivedTT += (totalReceived - totalReceivedOnl);
                totalResolvedOverdue += dt.resolvedOverdue;
                totalResolvedEarly += dt.resolvedEarly;
                totalResolved += (totalResolvedEarly + totalResolvedOverdue);
                totalUnresolved += dt.unresolved;
                totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
                totalUnresolvedOverdue += dt.unresolvedOverdue;
                data2.push({
                  stt: iagency2 + '.' + i2,
                  sector: sectorName,
                  received: dt.received,
                  receivedOnline: dt.receivedOnline,
                  receivedTT: (dt.received - dt.receivedOnline),
                  receivedOld: 0,
                  resolved: (dt.resolvedEarly + dt.resolvedOverdue),
                  resolvedEarly: dt.resolvedEarly,
                  resolvedOverdue: dt.resolvedOverdue,
                  unresolvedOverdue: dt.unresolvedOverdue,
                  unresolved: totalUnresolved,
                  unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
                  total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved
                });
              }
              if (agencyOld !== dt.agency.id) {
                // set value for array total
                if (localStorage.getItem('language') === 'vi') {
                  dataAgency2.push({
                    agency: iagency2 + '. TTHCC do ' + agencyName + ' tiếp nhận, giải quyết TTHC',
                    value: data2
                  });
                }
                else {
                  dataAgency2.push({
                    agency: iagency2 + '. Public administrative procedures for ' + agencyName + ' receive and settle administrative procedures',
                    value: data2
                  });
                }
                // end set value for array total
                i2 = 1;
                iagency2++;
                data2 = [];
                agencyOld = dt.agency.id;
                for (const n of dt.agency.name) {
                  if (n.languageId === this.selectedLang) {
                    agencyName = n.name;
                  }
                }

                for (const n of dt.sector.name) {
                  if (n.languageId === this.selectedLang) {
                    sectorName = n.name;
                  }
                }
                totalReceived += dt.received;
                totalReceivedOnl += dt.receivedOnline;
                totalReceivedTT += (totalReceived - totalReceivedOnl);
                totalResolvedOverdue += dt.resolvedOverdue;
                totalResolvedEarly += dt.resolvedEarly;
                totalResolved += (totalResolvedEarly + totalResolvedOverdue);
                totalUnresolved += dt.unresolved;
                totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
                totalUnresolvedOverdue += dt.unresolvedOverdue;
                data2.push({
                  stt: i2,
                  sector: sectorName,
                  received: dt.received,
                  receivedOnline: dt.receivedOnline,
                  receivedTT: (dt.received - dt.receivedOnline),
                  receivedOld: 0,
                  resolved: (dt.resolvedEarly + dt.resolvedOverdue),
                  resolvedEarly: dt.resolvedEarly,
                  resolvedOverdue: dt.resolvedOverdue,
                  unresolvedOverdue: dt.unresolvedOverdue,
                  unresolved: totalUnresolved,
                  unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
                  total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved
                });
              }
            }else {
              i2++;
              for (const n of dt.sector.name) {
                if (n.languageId === this.selectedLang) {
                  sectorName = n.name;
                }
              }
              totalReceived += dt.received;
              totalReceivedOnl += dt.receivedOnline;
              totalReceivedTT += (totalReceived - totalReceivedOnl);
              totalResolvedOverdue += dt.resolvedOverdue;
              totalResolvedEarly += dt.resolvedEarly;
              totalResolved += (totalResolvedEarly + totalResolvedOverdue);
              totalUnresolved += dt.unresolved;
              totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
              totalUnresolvedOverdue += dt.unresolvedOverdue;
              data2.push({
                stt: i2,
                sector: sectorName,
                received: dt.received,
                receivedOnline: dt.receivedOnline,
                receivedTT: (dt.received - dt.receivedOnline),
                receivedOld: 0,
                resolved: (dt.resolvedEarly + dt.resolvedOverdue),
                resolvedEarly: dt.resolvedEarly,
                resolvedOverdue: dt.resolvedOverdue,
                unresolvedOverdue: dt.unresolvedOverdue,
                unresolved: totalUnresolved,
                unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
                total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved
              });
            }
            if (i2 === data.numberOfElements) {
              if (localStorage.getItem('language') === 'vi') {
                dataTotal2.push({
                  agencyLevel: {
                    id: agencyLevelOld,
                    name: 'TTHC thuộc phạm vi thẩm quyền của UBND ' + agencyLevelName
                  },
                  value: new MatTableDataSource(dataAgency2)
                });
              }
              else {
                dataTotal2.push({
                  agencyLevel: {
                    id: agencyLevelOld,
                    name: 'Administrative procedures fall under the jurisdiction of the People\'s Committee ' + agencyLevelName
                  },
                  value: new MatTableDataSource(dataAgency2)
                });
              }
            }
            // end xet agency
          }
        }
        // end for
        data2 = [];
        data2.push({
          stt: '',
          sector: null,
          received: totalReceived,
          receivedOnline: totalReceivedOnl,
          receivedTT: totalReceivedTT,
          receivedOld: 0,
          resolvedOverdue: totalResolvedOverdue,
          resolvedEarly: totalResolvedEarly,
          resolved: totalResolved,
          unresolved: totalUnresolved,
          unresolvedHadTime: totalUnresolvedHadTime,
          unresolvedOverdue: totalUnresolvedOverdue,
          total: totalResolvedEarly + totalResolvedOverdue + totalUnresolved
        });
        dataAgency2 = [];
        dataAgency2.push({
          value: data2,
          agency: null
        });
        dataTotal2.push({
          agencyLevel: { id: '', name: (localStorage.getItem('language') === 'vi' ? 'Tổng cộng' : 'Total') },
          value: new MatTableDataSource(dataAgency2)
        });
        this.countResult2 = iTotal2 - 1;
        this.ELEMENTDATA2 = dataTotal2;
        this.dataSource2.data = this.ELEMENTDATA2;
      });
    }
    // end form 6d

    // form 6g qua han
    if (form === 3) {
      this.statisticalService.getSectorReport(searchString).subscribe(data => {
        this.ELEMENTDATA3 = [];
        let sectorName = '';
        let i3 = 0;
        const data3 = [];
        for (const dt of data.content) {
          i3++;
          for (const n of dt.sector.name) {
            if (n.languageId === this.selectedLang) {
              sectorName = n.name;
            }
          }
          data3.push({
            stt: i3,
            sector: sectorName,
            unresolvedOverdue: dt.unresolvedOverdue
          });
        }
        this.countResult3 = i3;
        this.ELEMENTDATA3 = data3;
        this.dataSource3.data = this.ELEMENTDATA3;
      });
    }
    // end form 6g qua han

    // form 6g quy dinh hanh chinh
    if (form === 4) {
      this.statisticalService.getProcedureReport(searchString).subscribe(data => {
        this.ELEMENTDATA4 = [];
        let data4 = [];
        const dataTotal4 = [];
        let sectorName = '';
        let sectorId = '';
        let procedureName = '';
        let i4 = 1;
        let iTotal4 = 0;
        // start for
        for (const dt of data.content) {
          if (sectorId === '' || sectorId !== dt.sector.id) {
            if (sectorId === '') {
              sectorId = dt.sector.id;
              iTotal4++;
              for (const n of dt.sector.name) {
                if (n.languageId === this.selectedLang) {
                  sectorName = n.name;
                }
              }
              for (const n of dt.procedure.translate) {
                if (n.languageId === this.selectedLang) {
                  procedureName = n.name;
                }
              }
              data4.push({
                stt: i4,
                procedure: procedureName
              });
            }

            if (sectorId !== dt.sector.id) {
              // set value for array total
              dataTotal4.push({
                sector: {
                  id: sectorId,
                  name: sectorName
                },
                value: new MatTableDataSource(data4)
              });
              // end set value for array total
              i4 = 1;
              iTotal4++;
              data4 = [];
              sectorId = dt.sector.id;
              for (const n of dt.sector.name) {
                if (n.languageId === this.selectedLang) {
                  sectorName = n.name;
                }
              }
              for (const n of dt.procedure.translate) {
                if (n.languageId === this.selectedLang) {
                  procedureName = n.name;
                }
              }
              data4.push({
                stt: i4,
                procedure: procedureName
              });
            }
          } else {
            i4++;
            for (const n of dt.procedure.translate) {
              if (n.languageId === this.selectedLang) {
                procedureName = n.name;
              }
            }
            data4.push({
              stt: i4,
              procedure: procedureName
            });
          }
          if (i4 === data.numberOfElements) {
            dataTotal4.push({
              sector: {
                id: sectorId,
                name: sectorName
              },
              value: new MatTableDataSource(data4)
            });
          }
        }
        // end for
        this.countResult4 = iTotal4 - 1;
        this.ELEMENTDATA4 = dataTotal4;
        this.dataSource4.data = this.ELEMENTDATA4;
      });
    }
    // end form 6g quy dinh hanh chinh
  }

  getListAgency(id = '') {
    this.statisticalService.getListAgency(id).subscribe(data => {
      for (const i of data.content) {
        this.agency.push({
          id: i.id,
          name: i.name,
          telephone: i.telephone
        });
      }
    });
  }

  // paginate
  paginate(event: any, type) {
    let searchString = '';
    switch (type) {
      case 0:
        this.pageIndex = event;
        searchString = '?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
          + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
        this.getReportForDossier(searchString, 0);
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        searchString = '?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
          + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
        this.getReportForDossier(searchString, 0);
        break;
    }
  }
  paginate1(event: any, type) {
    let searchString = '';
    switch (type) {
      case 0:
        this.pageIndex1 = event;
        searchString = '?page=' + (this.pageIndex1 - 1) + '&size=' + this.size1 + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
          + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
        this.getReportForDossier(searchString, 1);
        break;
      case 1:
        this.pageIndex1 = 1;
        this.page1 = 1;
        searchString = '?page=' + (this.pageIndex1 - 1) + '&size=' + this.size1 + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
          + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
        this.getReportForDossier(searchString, 1);
        break;
    }
  }
  paginate2(event: any, type) {
    let searchString = '';
    switch (type) {
      case 0:
        this.pageIndex2 = event;
        searchString = '?page=' + (this.pageIndex2 - 1) + '&size=' + this.size2 + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
          + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
        this.getReportForDossier(searchString, 2);
        break;
      case 1:
        this.pageIndex2 = 1;
        this.page2 = 1;
        searchString = '?page=' + (this.pageIndex2 - 1) + '&size=' + this.size2 + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
          + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
        this.getReportForDossier(searchString, 2);
        break;
    }
  }
  paginate3(event: any, type) {
    let searchString = '';
    switch (type) {
      case 0:
        this.pageIndex3 = event;
        searchString = '?page=' + (this.pageIndex3 - 1) + '&size=' + this.size3 + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
          + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
        this.getReportForDossier(searchString, 3);
        break;
      case 1:
        this.pageIndex3 = 1;
        this.page3 = 1;
        searchString = '?page=' + (this.pageIndex3 - 1) + '&size=' + this.size3 + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
          + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
        this.getReportForDossier(searchString, 3);
        break;
    }
  }
  paginate4(event: any, type) {
    let searchString = '';
    switch (type) {
      case 0:
        this.pageIndex4 = event;
        searchString = '?page=' + (this.pageIndex4 - 1) + '&size=' + this.size4 + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
          + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
        this.getReportForDossier(searchString, 4);
        break;
      case 1:
        this.pageIndex4 = 1;
        this.page4 = 1;
        searchString = '?page=' + (this.pageIndex4 - 1) + '&size=' + this.size4 + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
          + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
        this.getReportForDossier(searchString, 4);
        break;
    }
  }
  // end paginate

  importDataExcel() {
    const form = this.tabGroup.selecItedndex ? this.tabGroup.selecItedndex : 0;
    const searchString = '?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&to-date=' + (this.toDate + 'T23:59:59')
    + '&from-date=' + (this.fromDate + 'T00:00:00') + '&agency-id=' + this.agencyId;
    // form 6a
    if (form === 0) {
      this.statisticalService.getReportForDossier6a(searchString).subscribe(data => {
        this.ELEMENTDATA = [];
        const data0 = [];
        let sectorName = '';
        let i0 = 0;
        console.log(data.content);
        for (const dt of data.content) {
          for (const n of dt.sector.name) {
            if (n.languageId === this.selectedLang) {
              sectorName = n.name;
            }
          }
          data0.push({
            stt: i0 + 1,
            sector: sectorName,
            received: dt.received,
            receivedOnline: dt.receivedOnline,
            receivedOld: 0,
            receivedTT: (dt.received - dt.receivedOnline),
            resolved: (dt.resolvedEarly + dt.resolvedOverdue),
            resolvedEarly: dt.resolvedEarly,
            resolvedOverdue: dt.resolvedOverdue,
            unresolved: dt.unresolved,
            unresolvedHadTime: (dt.unresolved - dt.unresolvedOverdue),
            unresolvedOverdue: dt.unresolvedOverdue,
            total: dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved,
            resolvedEarly1: dt.resolvedEarly,
            resolvedOverdue1: dt.resolvedOverdue,
            unresolved1: dt.unresolved
          });
          i0++;
        }
        this.excelData = data0;
        this.exportToExcel(this.excelData, form);
      });
    }
    // end form 6a

    // form 6b
    if (form === 1) {
      this.statisticalService.getReportForDossier6b(searchString).subscribe(data => {
        this.ELEMENTDATA1 = [];
        let data1 = [];
        const dataTotal = [];
        let totalReceived = 0;
        let totalReceivedOnl = 0;
        let totalReceivedTT = 0;
        let totalResolvedOverdue = 0;
        let totalResolved = 0;
        let totalResolvedEarly = 0;
        let totalUnresolved = 0;
        let totalUnresolvedHadTime = 0;
        let totalUnresolvedOverdue = 0;
        let sectorName = '';
        let i1 = 1;
        let iTotal1 = 0; // data.numberOfElements;
        let agencyLevelOld = '';
        let agencyLevelName = '';
        // start for
        for (const dt of data.content) {
          if (agencyLevelOld === '' || agencyLevelOld !== dt.agencyLevel.id) {
            if (agencyLevelOld === '') {
              iTotal1++;
              agencyLevelOld = dt.agencyLevel.id;
              for (const n of dt.agencyLevel.name) {
                if (n.languageId === this.selectedLang) {
                  agencyLevelName = n.name;
                }
              }

              for (const n of dt.sector.name) {
                if (n.languageId === this.selectedLang) {
                  sectorName = n.name;
                }
              }
              totalReceived += dt.received;
              totalReceivedOnl += dt.receivedOnline;
              totalReceivedTT += (totalReceived - totalReceivedOnl);
              totalResolvedOverdue += dt.resolvedOverdue;
              totalResolvedEarly += dt.resolvedEarly;
              totalResolved += (totalResolvedEarly + totalResolvedOverdue);
              totalUnresolved += dt.unresolved;
              totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
              totalUnresolvedOverdue += dt.unresolvedOverdue;
              data1.push([
                i1, sectorName, dt.received, dt.receivedOnline, 0, (dt.received - dt.receivedOnline),
                (dt.resolvedEarly + dt.resolvedOverdue), dt.resolvedEarly, dt.resolvedOverdue,
                totalUnresolved, (dt.unresolved - dt.unresolvedOverdue), dt.unresolvedOverdue,
                (dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved), dt.resolvedEarly, dt.resolvedOverdue, dt.unresolved
              ]);
            }

            if (agencyLevelOld !== dt.agencyLevel.id) {
              // set value for array total
              if (localStorage.getItem('language') === 'vi') {
                dataTotal.push({
                  agencyLevel: ['TTHC thuộc phạm vi thẩm quyền của UBND ' + agencyLevelName],
                  isArray: 1,
                  value: data1
                });
              }
              else {
                dataTotal.push({
                  agencyLevel: [ 'Administrative procedures fall under the jurisdiction of the People\'s Committee ' + agencyLevelName ],
                  isArray: 1,
                  value: data1
                });
              }
              // end set value for array total
              i1 = 1;
              iTotal1++;
              data1 = [];
              agencyLevelOld = dt.agencyLevel.id;
              for (const n of dt.agencyLevel.name) {
                if (n.languageId === this.selectedLang) {
                  agencyLevelName = n.name;
                }
              }

              for (const n of dt.sector.name) {
                if (n.languageId === this.selectedLang) {
                  sectorName = n.name;
                }
              }
              totalReceived += dt.received;
              totalReceivedOnl += dt.receivedOnline;
              totalReceivedTT += (totalReceived - totalReceivedOnl);
              totalResolvedOverdue += dt.resolvedOverdue;
              totalResolvedEarly += dt.resolvedEarly;
              totalResolved += (totalResolvedEarly + totalResolvedOverdue);
              totalUnresolved += dt.unresolved;
              totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
              totalUnresolvedOverdue += dt.unresolvedOverdue;
              data1.push([
                i1, sectorName, dt.received, dt.receivedOnline, 0, (dt.received - dt.receivedOnline),
                (dt.resolvedEarly + dt.resolvedOverdue), dt.resolvedEarly, dt.resolvedOverdue,
                totalUnresolved, (dt.unresolved - dt.unresolvedOverdue), dt.unresolvedOverdue,
                (dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved), dt.resolvedEarly, dt.resolvedOverdue, dt.unresolved
              ]);
            }
          } else {
            i1++;
            for (const n of dt.sector.name) {
              if (n.languageId === this.selectedLang) {
                sectorName = n.name;
              }
            }
            totalReceived += dt.received;
            totalReceivedOnl += dt.receivedOnline;
            totalReceivedTT += (totalReceived - totalReceivedOnl);
            totalResolvedOverdue += dt.resolvedOverdue;
            totalResolvedEarly += dt.resolvedEarly;
            totalResolved += (totalResolvedEarly + totalResolvedOverdue);
            totalUnresolved += dt.unresolved;
            totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
            totalUnresolvedOverdue += dt.unresolvedOverdue;
            data1.push([
              i1, sectorName, dt.received, dt.receivedOnline, 0, (dt.received - dt.receivedOnline),
              (dt.resolvedEarly + dt.resolvedOverdue), dt.resolvedEarly, dt.resolvedOverdue,
              totalUnresolved, (dt.unresolved - dt.unresolvedOverdue), dt.unresolvedOverdue,
              (dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved), dt.resolvedEarly, dt.resolvedOverdue, dt.unresolved
            ]);
          }

          if (i1 === data.numberOfElements) {
            if (localStorage.getItem('language') === 'vi') {
              dataTotal.push({
                agencyLevel: ['TTHC thuộc phạm vi thẩm quyền của UBND ' + agencyLevelName],
                isArray: 1,
                value: data1
              });
            }
            else {
              dataTotal.push({
                agencyLevel: [ 'Administrative procedures fall under the jurisdiction of the People\'s Committee ' + agencyLevelName ],
                isArray: 1,
                value: data1
              });
            }
          }
        }
        this.footerData = [];
        let total = '';
        total = localStorage.getItem('language') === 'vi' ? 'Tổng cộng' : 'Total';
        this.footerData.push([
          total, '', totalReceived, totalReceivedOnl, 0, totalReceivedTT,
          totalResolved, totalResolvedEarly, totalResolvedOverdue,
          totalUnresolved, totalUnresolvedHadTime, totalUnresolvedOverdue,
          (totalResolvedEarly + totalResolvedOverdue + totalUnresolved), totalResolvedEarly, totalResolvedOverdue, totalUnresolved
        ]);
        // end for
        this.excelData = dataTotal;
        console.log(dataTotal);
        this.exportToExcel(this.excelData, form);
      });
    }
    // end form 6b

    // form 6d
    if (form === 2) {
      this.statisticalService.getReportForDossier6d(searchString).subscribe(data => {
        this.ELEMENTDATA2 = [];
        let data2 = [];
        const dataTotal2 = [];
        const dataAgency2 = [];
        let totalReceived = 0;
        let totalReceivedOnl = 0;
        let totalReceivedTT = 0;
        let totalResolvedOverdue = 0;
        let totalResolved = 0;
        let totalResolvedEarly = 0;
        let totalUnresolved = 0;
        let totalUnresolvedHadTime = 0;
        let totalUnresolvedOverdue = 0;
        let sectorName = '';
        let i2 = 1;
        let iagency2 = 1;
        let iTotal2 = 0;
        let agencyLevelOld = '';
        let agencyLevelName = '';
        let agencyOld = '';
        let agencyName = '';
        // start for
        for (const dt of data.content) {
          if (agencyLevelOld === '' || agencyLevelOld !== dt.agencyLevel.id) {
            if (agencyLevelOld === '') {
              iTotal2++;
              agencyLevelOld = dt.agencyLevel.id;
              for (const n of dt.agencyLevel.name) {
                if (n.languageId === this.selectedLang) {
                  agencyLevelName = n.name;
                }
              }
              // xet agency
              if (agencyOld === '' || agencyOld !== dt.agency.id) {
                i2 = 1;
                if (agencyOld === '') {
                  for (const n of dt.sector.name) {
                    if (n.languageId === this.selectedLang) {
                      sectorName = n.name;
                    }
                  }
                  for (const n of dt.agency.name) {
                    if (n.languageId === this.selectedLang) {
                      agencyName = n.name;
                    }
                  }
                  totalReceived += dt.received;
                  totalReceivedOnl += dt.receivedOnline;
                  totalReceivedTT += (totalReceived - totalReceivedOnl);
                  totalResolvedOverdue += dt.resolvedOverdue;
                  totalResolvedEarly += dt.resolvedEarly;
                  totalResolved += (totalResolvedEarly + totalResolvedOverdue);
                  totalUnresolved += dt.unresolved;
                  totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
                  totalUnresolvedOverdue += dt.unresolvedOverdue;
                  data2.push([
                    iagency2 + '.' + i2, sectorName, dt.received, dt.receivedOnline, 0, (dt.received - dt.receivedOnline),
                    (dt.resolvedEarly + dt.resolvedOverdue), dt.resolvedEarly, dt.resolvedOverdue,
                    totalUnresolved, (dt.unresolved - dt.unresolvedOverdue), dt.unresolvedOverdue,
                    (dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved), dt.resolvedEarly, dt.resolvedOverdue, dt.unresolved
                  ]);
                }
                if (agencyOld !== dt.agency.id) {
                  // set value for array total
                  if (localStorage.getItem('language') === 'vi') {
                    dataAgency2.push([
                      [iagency2, 'TTHCC do ' + agencyName + ' tiếp nhận, giải quyết TTHC'], data2
                    ]);
                  }
                  else {
                    dataAgency2.push([
                      [iagency2, 'Public administrative procedures for ' + agencyName + ' receive and settle administrative procedures'],
                      data2
                    ]);
                  }
                  // end set value for array total
                  i2 = 1;
                  iagency2++;
                  data2 = [];
                  agencyOld = dt.agency.id;
                  for (const n of dt.agency.name) {
                    if (n.languageId === this.selectedLang) {
                      agencyName = n.name;
                    }
                  }

                  for (const n of dt.sector.name) {
                    if (n.languageId === this.selectedLang) {
                      sectorName = n.name;
                    }
                  }
                  totalReceived += dt.received;
                  totalReceivedOnl += dt.receivedOnline;
                  totalReceivedTT += (totalReceived - totalReceivedOnl);
                  totalResolvedOverdue += dt.resolvedOverdue;
                  totalResolvedEarly += dt.resolvedEarly;
                  totalResolved += (totalResolvedEarly + totalResolvedOverdue);
                  totalUnresolved += dt.unresolved;
                  totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
                  totalUnresolvedOverdue += dt.unresolvedOverdue;
                  data2.push([
                    iagency2 + '.' + i2, sectorName, dt.received, dt.receivedOnline, 0, (dt.received - dt.receivedOnline),
                    (dt.resolvedEarly + dt.resolvedOverdue), dt.resolvedEarly, dt.resolvedOverdue,
                    totalUnresolved, (dt.unresolved - dt.unresolvedOverdue), dt.unresolvedOverdue,
                    (dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved), dt.resolvedEarly, dt.resolvedOverdue, dt.unresolved
                  ]);
                }
              }else {
                i2++;
                for (const n of dt.sector.name) {
                  if (n.languageId === this.selectedLang) {
                    sectorName = n.name;
                  }
                }
                totalReceived += dt.received;
                totalReceivedOnl += dt.receivedOnline;
                totalReceivedTT += (totalReceived - totalReceivedOnl);
                totalResolvedOverdue += dt.resolvedOverdue;
                totalResolvedEarly += dt.resolvedEarly;
                totalResolved += (totalResolvedEarly + totalResolvedOverdue);
                totalUnresolved += dt.unresolved;
                totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
                totalUnresolvedOverdue += dt.unresolvedOverdue;
                data2.push([
                  iagency2 + '.' + i2, sectorName, dt.received, dt.receivedOnline, 0, (dt.received - dt.receivedOnline),
                  (dt.resolvedEarly + dt.resolvedOverdue), dt.resolvedEarly, dt.resolvedOverdue,
                  totalUnresolved, (dt.unresolved - dt.unresolvedOverdue), dt.unresolvedOverdue,
                  (dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved), dt.resolvedEarly, dt.resolvedOverdue, dt.unresolved
                ]);
              }
              if (i2 === data.numberOfElements) {
                if (localStorage.getItem('language') === 'vi') {
                  dataTotal2.push({
                    name: ['TTHC thuộc phạm vi thẩm quyền của UBND ' + agencyLevelName],
                    isArray: 2,
                    agency: dataAgency2
                  });
                }
                else {
                  dataTotal2.push({
                    name: ['Administrative procedures fall under the jurisdiction of the People\'s Committee ' + agencyLevelName],
                    isArray: 2,
                    agency: dataAgency2
                  });
                }
              }
              // end xet agency
            }

            if (agencyLevelOld !== dt.agencyLevel.id) {
              // set value for array total
              if (localStorage.getItem('language') === 'vi') {
                dataTotal2.push({
                  name: ['TTHC thuộc phạm vi thẩm quyền của UBND ' + agencyLevelName],
                  isArray: 2,
                  agency: dataAgency2
                });
              }
              else {
                dataTotal2.push({
                  name: ['Administrative procedures fall under the jurisdiction of the People\'s Committee ' + agencyLevelName],
                  isArray: 2,
                  agency: dataAgency2
                });
              }
              // end set value for array total
              iTotal2++;
              agencyLevelOld = dt.agencyLevel.id;
              for (const n of dt.agencyLevel.name) {
                if (n.languageId === this.selectedLang) {
                  agencyLevelName = n.name;
                }
              }
            }
          } else {
            // xet agency
            if (agencyOld === '' || agencyOld !== dt.agency.id) {
              i2 = 1;
              if (agencyOld === '') {
                for (const n of dt.sector.name) {
                  if (n.languageId === this.selectedLang) {
                    sectorName = n.name;
                  }
                }
                for (const n of dt.agency.name) {
                  if (n.languageId === this.selectedLang) {
                    agencyName = n.name;
                  }
                }
                totalReceived += dt.received;
                totalReceivedOnl += dt.receivedOnline;
                totalReceivedTT += (totalReceived - totalReceivedOnl);
                totalResolvedOverdue += dt.resolvedOverdue;
                totalResolvedEarly += dt.resolvedEarly;
                totalResolved += (totalResolvedEarly + totalResolvedOverdue);
                totalUnresolved += dt.unresolved;
                totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
                totalUnresolvedOverdue += dt.unresolvedOverdue;
                data2.push([
                  iagency2 + '.' + i2, sectorName, dt.received, dt.receivedOnline, 0, (dt.received - dt.receivedOnline),
                  (dt.resolvedEarly + dt.resolvedOverdue), dt.resolvedEarly, dt.resolvedOverdue,
                  totalUnresolved, (dt.unresolved - dt.unresolvedOverdue), dt.unresolvedOverdue,
                  (dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved), dt.resolvedEarly, dt.resolvedOverdue, dt.unresolved
                ]);
              }
              if (agencyOld !== dt.agency.id) {
                // set value for array total
                if (localStorage.getItem('language') === 'vi') {
                  dataAgency2.push([
                    [iagency2, 'TTHCC do ' + agencyName + ' tiếp nhận, giải quyết TTHC'], data2
                  ]);
                }
                else {
                  dataAgency2.push([
                    [iagency2, 'Public administrative procedures for ' + agencyName + ' receive and settle administrative procedures'],
                    data2
                  ]);
                }
                // end set value for array total
                i2 = 1;
                iagency2++;
                data2 = [];
                agencyOld = dt.agency.id;
                for (const n of dt.agency.name) {
                  if (n.languageId === this.selectedLang) {
                    agencyName = n.name;
                  }
                }

                for (const n of dt.sector.name) {
                  if (n.languageId === this.selectedLang) {
                    sectorName = n.name;
                  }
                }
                totalReceived += dt.received;
                totalReceivedOnl += dt.receivedOnline;
                totalReceivedTT += (totalReceived - totalReceivedOnl);
                totalResolvedOverdue += dt.resolvedOverdue;
                totalResolvedEarly += dt.resolvedEarly;
                totalResolved += (totalResolvedEarly + totalResolvedOverdue);
                totalUnresolved += dt.unresolved;
                totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
                totalUnresolvedOverdue += dt.unresolvedOverdue;
                data2.push([
                  iagency2 + '.' + i2, sectorName, dt.received, dt.receivedOnline, 0, (dt.received - dt.receivedOnline),
                  (dt.resolvedEarly + dt.resolvedOverdue), dt.resolvedEarly, dt.resolvedOverdue,
                  totalUnresolved, (dt.unresolved - dt.unresolvedOverdue), dt.unresolvedOverdue,
                  (dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved), dt.resolvedEarly, dt.resolvedOverdue, dt.unresolved
                ]);
              }
            }else {
              i2++;
              for (const n of dt.sector.name) {
                if (n.languageId === this.selectedLang) {
                  sectorName = n.name;
                }
              }
              totalReceived += dt.received;
              totalReceivedOnl += dt.receivedOnline;
              totalReceivedTT += (totalReceived - totalReceivedOnl);
              totalResolvedOverdue += dt.resolvedOverdue;
              totalResolvedEarly += dt.resolvedEarly;
              totalResolved += (totalResolvedEarly + totalResolvedOverdue);
              totalUnresolved += dt.unresolved;
              totalUnresolvedHadTime += (dt.unresolved - dt.unresolvedOverdue);
              totalUnresolvedOverdue += dt.unresolvedOverdue;
              data2.push([
                iagency2 + '.' + i2, sectorName, dt.received, dt.receivedOnline, 0, (dt.received - dt.receivedOnline),
                (dt.resolvedEarly + dt.resolvedOverdue), dt.resolvedEarly, dt.resolvedOverdue,
                totalUnresolved, (dt.unresolved - dt.unresolvedOverdue), dt.unresolvedOverdue,
                (dt.resolvedEarly + dt.resolvedOverdue + dt.unresolved), dt.resolvedEarly, dt.resolvedOverdue, dt.unresolved
              ]);
            }
            if (i2 === data.numberOfElements) {
              if (localStorage.getItem('language') === 'vi') {
                dataTotal2.push({
                  name: ['TTHC thuộc phạm vi thẩm quyền của UBND ' + agencyLevelName],
                  isArray: 2,
                  value: dataAgency2
                });
              }
              else {
                dataTotal2.push({
                  name: ['Administrative procedures fall under the jurisdiction of the People\'s Committee ' + agencyLevelName],
                  isArray: 2,
                  value: dataAgency2
                });
              }
            }
            // end xet agency
          }
        }
        // end for
        this.footerData = [];
        let total = '';
        total = localStorage.getItem('language') === 'vi' ? 'Tổng cộng' : 'Total';
        this.footerData.push([
          total, '', totalReceived, totalReceivedOnl, 0, totalReceivedTT,
          totalResolved, totalResolvedEarly, totalResolvedOverdue,
          totalUnresolved, totalUnresolvedHadTime, totalUnresolvedOverdue,
          (totalResolvedEarly + totalResolvedOverdue + totalUnresolved), totalResolvedEarly, totalResolvedOverdue, totalUnresolved
        ]);
        this.excelData = dataTotal2;
        console.log(dataTotal2);
        this.exportToExcel(this.excelData, form);
      });
    }
    // end form 6d

    // form 6g qua han
    if (form === 3) {
      this.statisticalService.getSectorReport(searchString).subscribe(data => {
        this.ELEMENTDATA3 = [];
        let sectorName = '';
        let i3 = 0;
        const data3 = [];
        for (const dt of data.content) {
          i3++;
          for (const n of dt.sector.name) {
            if (n.languageId === this.selectedLang) {
              sectorName = n.name;
            }
          }
          data3.push({
            stt: i3,
            sector: sectorName,
            unresolvedOverdue: dt.unresolvedOverdue,
            cause: '',
            note: ''
          });
        }
        this.excelData = data3;
        this.exportToExcel(this.excelData, form);
      });
    }
    // end form 6g qua han

    // form 6g quy dinh hanh chinh
    if (form === 4) {
      this.statisticalService.getProcedureReport(searchString).subscribe(data => {
        this.ELEMENTDATA4 = [];
        let data4 = [];
        const dataTotal4 = [];
        let sectorName = '';
        let sectorId = '';
        let procedureName = '';
        let i4 = 1;
        let iTotal4 = 0;
        // start for
        for (const dt of data.content) {
          if (sectorId === '' || sectorId !== dt.sector.id) {
            if (sectorId === '') {
              sectorId = dt.sector.id;
              iTotal4++;
              for (const n of dt.sector.name) {
                if (n.languageId === this.selectedLang) {
                  sectorName = n.name;
                }
              }
              for (const n of dt.procedure.translate) {
                if (n.languageId === this.selectedLang) {
                  procedureName = n.name;
                }
              }
              data4.push([i4, procedureName]);
            }

            if (sectorId !== dt.sector.id) {
              // set value for array total
              dataTotal4.push({
                sector: [
                  sectorName
                ],
                isArray: 1,
                value: data4
              });
              // end set value for array total
              i4 = 1;
              iTotal4++;
              data4 = [];
              sectorId = dt.sector.id;
              for (const n of dt.sector.name) {
                if (n.languageId === this.selectedLang) {
                  sectorName = n.name;
                }
              }
              for (const n of dt.procedure.translate) {
                if (n.languageId === this.selectedLang) {
                  procedureName = n.name;
                }
              }
              data4.push([i4, procedureName ]);
            }
          } else {
            i4++;
            for (const n of dt.procedure.translate) {
              if (n.languageId === this.selectedLang) {
                procedureName = n.name;
              }
            }
            data4.push([i4, procedureName]);
          }
          if (i4 === data.numberOfElements) {
            dataTotal4.push({
              sector: [
                sectorName
              ],
              isArray: 1,
              value: data4
            });
          }
        }
        // end for
        this.excelData = dataTotal4;
        console.log(dataTotal4);
        this.exportToExcel(this.excelData, form);
      });
    }
    // end form 6g quy dinh hanh chinh
  }

  exportToExcel(data, form) {
    if (form === 0 || form === 1 || form === 2){
      let title = '';
      let total = '';
      // let subColumns: any[] = [];
      let nameReport = '';
      let subNameReport = '';

      const formObj = this.searchForm.getRawValue();
      let fromDateExcel = '';
      let toDateExcel = '';
      if (formObj.fromDate != null || formObj.fromDate !== '') {
        fromDateExcel = this.datepipe.transform(formObj.fromDate, 'dd/MM/yyyy');
      }
      if (formObj.toDate != null || formObj.toDate !== '') {
        toDateExcel = this.datepipe.transform(formObj.toDate, 'dd/MM/yyyy');
      }
      let name = '';
      if (localStorage.getItem('language') === 'vi') {
        title = 'TÌNH HÌNH, KẾT QUẢ GIẢI QUYẾT TTHC TẠI CƠ QUAN, ĐƠN VỊ TRỰC TIẾP GIẢI QUYẾT TTHC';
        name = 'thong ke ho so theo thu tuc ' + this.datepipe.transform(new Date(), 'dd/MM/yyyy');
        total = 'Tổng cộng';
        switch (form){
          case 0:
            nameReport = 'Biểu số 06a/VPCP/KSTT';
            break;
          case 1:
            nameReport = 'Biểu số 06b/VPCP/KSTT';
            break;
          case 2:
            nameReport = 'Biểu số 06d/VPCP/KSTT';
            break;
        }
        subNameReport = 'Ban hành theo Thông tư số 02/2017/TT-VPCP ngày 31/10/2017';
      } else if (localStorage.getItem('language') === 'en') {
        name = 'procedureal statistics ' + this.datepipe.transform(new Date(), 'dd/MM/yyyy');
        title = 'SITUATION AND RESULTS OF SETTLEMENT OF ADMINISTRATIVE PROCEDURES AT THE AGENCY AND UNITS DIRECTLY DEALING WITH ADMINISTRATIVE PROCEDURES';
        switch (form){
          case 0:
            nameReport = 'Form No. 06a / VPCP / KSTT';
            break;
          case 1:
            nameReport = 'Form No. 06b / VPCP / KSTT';
            break;
          case 2:
            nameReport = 'Form No. 06d / VPCP / KSTT';
            break;
        }
        total = 'Total';
        subNameReport = 'Issued under Circular No. 02/2017 / TT-VPCP dated October 31, 2017';
      }
      let subTitle = '';
      if (fromDateExcel !== '' && toDateExcel !== '') {
        subTitle = fromDateExcel + ' - ' + toDateExcel;
      }

      if (form === 0){
        const sumReceived = data.reduce((sum, item) => sum + item.received, 0);
        const sumReceivedOnl = data.reduce((sum, item) => sum + item.receivedOnline, 0);
        const sumReceivedTT = data.reduce((sum, item) => sum + item.receivedTT, 0);
        const sumReceivedOld = data.reduce((sum, item) => sum + item.receivedOld, 0);
        const sumResolved = data.reduce((sum, item) => sum + item.resolved, 0);
        const sumResolvedEarly = data.reduce((sum, item) => sum + item.resolvedEarly, 0);
        const sumResolvedOverdue = data.reduce((sum, item) => sum + item.resolvedOverdue, 0);
        const sumUnresolvedOverdue = data.reduce((sum, item) => sum + item.unresolvedOverdue, 0);
        const sumUnresolved = data.reduce((sum, item) => sum + item.unresolved, 0);
        const sumUnresolvedHadTime = data.reduce((sum, item) => sum + item.unresolvedHadTime, 0);
        const sumTotal = data.reduce((sum, item) => sum + item.total, 0);
        this.footerData.push([
          total, '', sumReceived, sumReceivedOnl, sumReceivedOld, sumReceivedTT,
          sumResolved, sumResolvedEarly, sumResolvedOverdue,
          sumUnresolved, sumUnresolvedHadTime, sumUnresolvedOverdue,
          sumTotal, sumResolvedEarly, sumResolvedOverdue, sumUnresolved
        ]);
      }
      // tslint:disable-next-line: max-line-length
      this.exportExcel.exportAsExcelFileForm6a(title, subTitle, nameReport, subNameReport, data, this.footerData, name, 'Sheet1', this.agencyNameExcel);
    }
    else if (form === 3) {
      let title = '';
      let subColumns: any[] = [];
      let nameReport = '';
      let subNameReport = '';

      const formObj = this.searchForm.getRawValue();
      let fromDateExcel = '';
      let toDateExcel = '';
      if (formObj.fromDate != null || formObj.fromDate !== '') {
        fromDateExcel = this.datepipe.transform(formObj.fromDate, 'dd/MM/yyyy');
      }
      if (formObj.toDate != null || formObj.toDate !== '') {
        toDateExcel = this.datepipe.transform(formObj.toDate, 'dd/MM/yyyy');
      }
      let name = '';
      if (localStorage.getItem('language') === 'vi') {
        title = 'NGUYÊN NHÂN QUÁ HẠN ĐỐI VỚI CÁC TRƯỜNG HỢP TỒN ĐỌNG TRONG GIẢI QUYẾT THỦ TỤC HÀNH CHÍNH (Quý/năm)';
        name = 'thong ke 6g qua han ' + this.datepipe.transform(new Date(), 'dd/MM/yyyy');
        nameReport = 'Biểu số 06a/VPCP/KSTT';
        subNameReport = 'Ban hành theo Thông tư số 02/2017/TT-VPCP ngày 31/10/2017';
        this.columns = ['STT', 'Lĩnh vực, công việc giải quyết', 'Số lượng hồ sơ', 'Nguyên nhân quá hạn', 'Ghi chú'];
        subColumns = ['(1)', '(2)', '(3)', '(4)', '(5)'];
      } else if (localStorage.getItem('language') === 'en') {
        name = 'report 6g overdue ' + this.datepipe.transform(new Date(), 'dd/MM/yyyy');
        title = 'CAUSES OF LONG-TERM CONCERNS IN RESOLUTION OF ADMINISTRATIVE PROCESS (QUARTER/YEAR)';
        nameReport = 'Form No. 06a / VPCP / KSTT';
        subNameReport = 'Issued under Circular No. 02/2017 / TT-VPCP dated October 31, 2017';
        this.columns = ['No.', 'Areas of expertise, settlement work', 'Number of dossiers', 'The cause is overdue', 'Note'];
        subColumns = ['(1)', '(2)', '(3)', '(4)', '(5)'];
      }
      let subTitle = '';
      if (fromDateExcel !== '' && toDateExcel !== '') {
        subTitle = fromDateExcel + ' - ' + toDateExcel;
      }
      this.exportExcel.exportAsExcelFileSimple(title, subTitle, nameReport, subNameReport, this.columns, subColumns, this.excelData, name, 'Sheet1', this.agencyNameExcel);
    }
    else {
      let title = '';
      let subColumns: any[] = [];
      let nameReport = '';
      let subNameReport = '';

      const formObj = this.searchForm.getRawValue();
      let fromDateExcel = '';
      let toDateExcel = '';
      if (formObj.fromDate != null || formObj.fromDate !== '') {
        fromDateExcel = this.datepipe.transform(formObj.fromDate, 'dd/MM/yyyy');
      }
      if (formObj.toDate != null || formObj.toDate !== '') {
        toDateExcel = this.datepipe.transform(formObj.toDate, 'dd/MM/yyyy');
      }
      let name = '';
      if (localStorage.getItem('language') === 'vi') {
        title = 'NGUYÊN NHÂN QUÁ HẠN ĐỐI VỚI CÁC TRƯỜNG HỢP TỒN ĐỌNG TRONG GIẢI QUYẾT THỦ TỤC HÀNH CHÍNH (Quý/năm)';
        name = 'thong ke 6g quy dinh hanh chinh ' + this.datepipe.transform(new Date(), 'dd/MM/yyyy');
        nameReport = 'Biểu số 06a/VPCP/KSTT';
        subNameReport = 'Ban hành theo Thông tư số 02/2017/TT-VPCP ngày 31/10/2017';
        this.columns = ['STT', 'Tên TTHC', 'Nội dung vướng mắc', 'Văn bản QPPL'];
        subColumns = ['(1)', '(2)', '(3)', '(4)'];
      } else if (localStorage.getItem('language') === 'en') {
        name = 'report 6g procedure ' + this.datepipe.transform(new Date(), 'dd/MM/yyyy');
        title = 'CAUSES OF LONG-TERM CONCERNS IN RESOLUTION OF ADMINISTRATIVE PROCESS (QUARTER/YEAR)';
        nameReport = 'Form No. 06a / VPCP / KSTT';
        subNameReport = 'Issued under Circular No. 02/2017 / TT-VPCP dated October 31, 2017';
        this.columns = ['No.', 'Name of the administrative procedure', 'Content problems', 'Legal documents'];
        subColumns = ['(1)', '(2)', '(3)', '(4)'];
      }
      let subTitle = '';
      if (fromDateExcel !== '' && toDateExcel !== '') {
        subTitle = fromDateExcel + ' - ' + toDateExcel;
      }
      this.exportExcel.exportAsExcelFileSimple(title, subTitle, nameReport, subNameReport, this.columns, subColumns, this.excelData, name, 'Sheet1', this.agencyNameExcel);
    }
  }
}
