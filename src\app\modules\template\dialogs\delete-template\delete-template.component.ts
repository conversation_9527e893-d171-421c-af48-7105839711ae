import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TemplateService } from 'src/app/data/service/template/template.service';
import {DeploymentService} from 'data/service/deployment.service';

@Component({
  selector: 'app-delete-template',
  templateUrl: './delete-template.component.html',
  styleUrls: ['./delete-template.component.scss']
})
export class DeleteTemplateComponent implements OnInit {

  formId: string;
  formName: string;
  formFilePath: string;
  listDefaultConfigId = [];
  listDefaultConfigIdMulti = [];
  flag = 1;
  env: any = this.deploymentService.getAppDeployment()?.env;
  turnOffBasePad: any = false;

  constructor(
    public dialogRef: MatDialogRef<DeleteTemplateComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDeleteDialogModel,
    private deploymentService: DeploymentService,
    private templateService: TemplateService
  ) {
    this.formId = data.id;
    this.formName = data.name;
    this.formFilePath = data.filepath;
  }

  ngOnInit(): void {
    this.checkISO();
  }

  async onConfirm() {
    if(!this.turnOffBasePad){
      await this.checkConfig();
      if (this.flag === 0) {
        this.dialogRef.close(false);
      } else {
        const pathParam = '?path=' + this.formFilePath;
        this.templateService.deleteFile(pathParam).subscribe(result => {
        });
        this.templateService.deleteTemplate(this.formId).subscribe(data => {
          this.dialogRef.close(true);
        }, err => {
          this.dialogRef.close(false);
        });
      }
    }else {
      const pathParam = '?path=' + this.formFilePath;
      this.templateService.deleteFile(pathParam).subscribe(result => {
      });
      this.templateService.deleteTemplate(this.formId).subscribe(data => {
        this.dialogRef.close(true);
      }, err => {
        this.dialogRef.close(false);
      });
    }
  }

  onDismiss() {
    this.dialogRef.close();
  }

  async checkConfig() {
    const checkProcedureConfig = await this.templateService.getTotalPagesProcedureConfig(this.formId);
    const checkAgencyConfig = await this.templateService.getTotalPagesAgencyConfig(this.formId);
    this.getListDefaultConfig();
    // tslint:disable-next-line: max-line-length
    if (checkProcedureConfig.content.length > 0 || checkAgencyConfig.content.length > 0 || this.listDefaultConfigId.length > 0 || this.listDefaultConfigIdMulti.length > 0) {
      this.flag = 0;
    } else {
      this.flag = 1;
    }
  }

  getListDefaultConfig() {
    const listDefaultConfigIdMultiObj: any = {};
    const listDefaultConfigIdMultiArr = [];

    this.templateService.getListDefaultConfig().subscribe(data => {
      for (let i = 0; i < data.numberOfElements; i++) {
        if (data.content[i].template.length > 1) {
          // tslint:disable-next-line: prefer-for-of
          for (let j = 0; j < data.content[i].template.length; j++) {
            listDefaultConfigIdMultiArr.push(data.content[i].template[j].id);
          }
          listDefaultConfigIdMultiObj.idConfig = data.content[i].id;
          listDefaultConfigIdMultiObj.idTemplate = listDefaultConfigIdMultiArr;
          this.listDefaultConfigIdMulti.push(listDefaultConfigIdMultiObj);
        } else {
          this.listDefaultConfigId.push(data.content[i].id);
        }
      }
    }, err => {
      console.log(err);
    });
  }

  checkISO(){
    if(!!this.env?.isISO && this.env.isISO?.enable){
      this.turnOffBasePad = this.env.isISO?.turnOffBasePad
    }
  }

}

export class ConfirmDeleteDialogModel {
  constructor(public id: string, public name: string, public filepath: string) {
  }
}
