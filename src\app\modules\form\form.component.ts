import { Component, OnInit, ChangeDetectorRef, AfterViewInit, ViewChild, ElementRef  } from '@angular/core';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { Router } from '@angular/router';
import { FormService } from 'src/app/data/service/form/form.service';
import { EnvService } from 'src/app/core/service/env.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';
import { AddFormComponent, ConfirmAddFormDialogModel } from 'src/app/modules/form/dialogs/add-form/add-form.component';
import { UpdateFormComponent, ConfirmUpdateFormDialogModel } from 'src/app/modules/form/dialogs/update-form/update-form.component';
import { DeleteFormComponent, ConfirmDeleteDialogModel } from 'src/app/modules/form/dialogs/delete-form/delete-form.component';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';

@Component({
  selector: 'app-form',
  templateUrl: './form.component.html',
  styleUrls: ['./form.component.scss', '../../app.component.scss']
})

export class FormComponent implements OnInit, AfterViewInit {
  config = this.envService.getConfig();
  selectedLang: string;

  searchForm = new FormGroup({
    keyword: new FormControl('')
  });

  listForm = [];
  displayedColumns: string[] = ['stt', 'code', 'name', 'status', 'action'];
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;

  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;

  constructor(
    private router: Router,
    private keycloak: KeycloakService,
    private dialog: MatDialog,
    private formService: FormService,
    private envService: EnvService,
    private cdRef: ChangeDetectorRef,
    private snackbarService: SnackbarService,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }

  ngOnInit(): void {
    const searchString = '?page=0&size=10&spec=page&keyword=';
    this.getListForm(searchString);
    this.selectedLang = localStorage.getItem('language');
  }

  onConfirmSearch() {
    const formObj = this.searchForm.getRawValue();
    const searchString = '?page=0&size=' + this.size + '&spec=page&keyword=' + formObj.keyword;
    this.getListForm(searchString);
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  // ========================================================== Manual function

  getListForm(searchString) {
    this.formService.getListForm(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        this.getListForm('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=');
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.getListForm('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=');
        break;
    }
  }

  getStatus(status) {
    switch (status) {
      case 0:
        return 'Đóng';
      case 1:
        return 'Mở';
    }
  }

  addFormDialog() {
    const dialogData = new ConfirmAddFormDialogModel();
    const dialogRef = this.dialog.open(AddFormComponent, {
      width: '600px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      const content = 'Giấy tờ "' + res.name + '"';
      if (res.status === true) {
        const totalPage = Math.ceil((this.countResult + 1) / this.size);
        this.paginate(totalPage, 0);
        this.snackbarService.openSnackBar(1, this.envService.getTranslateNotificationLabel(this.selectedLang, 'success', 'post'), content, 'success_notification', this.config.expiredTime);
      }
      if (res.status === false) {
        this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'post'), content, 'error_notification', this.config.expiredTime);
      }
    });
  }

  updateFormDialog(id) {
    const dialogData = new ConfirmUpdateFormDialogModel(id);
    const dialogRef = this.dialog.open(UpdateFormComponent, {
      width: '600px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      const content = 'Giấy tờ "' + res.name + '"';
      if (res.status === true) {
        this.snackbarService.openSnackBar(1, this.envService.getTranslateNotificationLabel(this.selectedLang, 'success', 'put'), content, 'success_notification', this.config.expiredTime);
        this.paginate(this.pageIndex, 0);
      }
      if (res.status === false) {
        this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'put'), content, 'error_notification', this.config.expiredTime);
      }
    });
  }

  deleteFormDialog(id, name) {
    const dialogData = new ConfirmDeleteDialogModel(id, name);
    const dialogRef = this.dialog.open(DeleteFormComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    const content = 'Giấy tờ "' + name + '"';
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res === true) {
        this.snackbarService.openSnackBar(1, this.envService.getTranslateNotificationLabel(this.selectedLang, 'success', 'delete'), content, 'success_notification', this.config.expiredTime);
        this.paginate(this.pageIndex, 0);
      }
      if (res === false) {
        this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'delete'), content, 'error_notification', this.config.expiredTime);
      }
    });
  }
}
