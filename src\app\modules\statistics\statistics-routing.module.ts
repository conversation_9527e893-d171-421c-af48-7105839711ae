import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from 'src/app/core/guard/auth.guard';
import { AdminLayoutComponent } from 'src/app/layouts/admin/admin-layout/admin-layout.component';

const routes: Routes = [
  {
    path: '',
    component: AdminLayoutComponent,
    children: [
      {
        path: 'dossier-resolution',
        loadChildren: () => import('./pages/dossier-resolution/dossier-resolution.module').then(m => m.DossierResolutionModule),
      },
    ],
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['statisticsAdminMaster', 'statisticsDossierResolutionOverview']
    }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StatisticsRoutingModule { }
