{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"web-reporter": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/web", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "i18nFile": "src/locale/messages.vi.xlf", "i18nFormat": "xlf", "i18nLocale": "vi", "i18nMissingTranslation": "error", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "production-vi": {"outputPath": "dist/web/vi", "baseHref": "/vi/", "i18nFile": "src/locale/messages.vi.xlf", "i18nFormat": "xlf", "i18nLocale": "vi", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "production-en": {"outputPath": "dist/web/en", "baseHref": "/en/", "i18nFile": "src/locale/messages.en.xlf", "i18nFormat": "xlf", "i18nLocale": "en", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "web-reporter:build"}, "configurations": {"production": {"browserTarget": "web-reporter:build:production"}, "production-vi": {"browserTarget": "web-reporter:build:production-vi"}, "production-en": {"browserTarget": "web-reporter:build:production-en"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "web-reporter:build", "outputPath": "src/locale"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "web-reporter:serve"}, "configurations": {"production": {"devServerTarget": "web-reporter:serve:production"}}}}}}, "defaultProject": "web-reporter", "cli": {"analytics": "19e983ca-63ab-4b30-a33e-129cbef7dfcd"}}