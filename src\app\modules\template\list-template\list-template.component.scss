// ================================= searchForm
::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    color: transparent;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #dddddd;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
}

::ng-deep .frm_searchbar .searchForm .searchBtn {
    margin-top: 0.3em;
    background-color: #ce7a58;
    color: #fff;
    height: 3.2em;
}

::ng-deep .frm_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
}

::ng-deep .frm_searchbar .mat-form-field-label-wrapper {
    top: -1em;
}

::ng-deep .frm_searchbar .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
    font-size: 18px;
    margin-bottom: 1em;
}

::ng-deep .frm_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    color: #ce7a58;
    font-size: 18px;
    margin-bottom: 1em;
}

// ================================= frm_main
.frm_main {
    border-radius: 4px;
    padding: 1em;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);
}

.frm_main .btn_add {
    background-color: #e8e8e8;
    color: #666;
    float: right;
}

.frm_main .btn_add .mat-icon {
    color: #ce7a58;
}

// ================================= frm_tbl
.frm_tbl {
    margin-top: 3.5em;
}

.frm_tbl table {
    border-radius: 4px;
    border: 1px solid #ececec;
    width: 100%;
}

::ng-deep .frm_tbl .mat-header-row {
    background-color: #e8e8e8;
}

::ng-deep .frm_tbl .mat-header-row .mat-header-cell {
    color: #495057;
    font-size: 14px;
    // display: grid;
}

::ng-deep .frm_tbl .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
}

::ng-deep .frm_tbl .mat-column-stt {
    margin-right: 0.5em;
    flex: 0 0 5%;
}

::ng-deep .frm_tbl .mat-column-name {
    margin-right: 1em;
    flex: 1 0 5%;
}

::ng-deep .frm_tbl .mat-column-subsystem {
    margin-right: 1em;
    flex: 2 0 5%;
}

::ng-deep .frm_tbl .mat-column-typeName {
    margin-right: 1em;
    flex: 1 0 5%;
}

::ng-deep .frm_tbl .mat-column-fileName {
    margin-right: 1em;
    flex: 1 0 5%;
}

::ng-deep .frm_tbl .mat-column-action {
    flex: 0 0 5%;
    float: right;
}

::ng-deep {
    .mat-tooltip {
        font-size: 13px;
        max-width: unset !important;
        overflow-wrap: anywhere;
    }
}

::ng-deep .frm_tbl .btn_downloadForm {
    padding: 0;
    color: #ce7a58;
}

::ng-deep .frm_tbl .cell_code {
    margin-right: 1em;
    color: #ce7a58;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    overflow-wrap: anywhere;
}

::ng-deep .frm_tbl .fileNameCell {
    color: #ce7a58;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    overflow-wrap: anywhere;
}

::ng-deep .frm_tbl .btn_downloadForm .mat-button-wrapper {
    display: flex;
}

::ng-deep .frm_tbl .btn_downloadForm .mat-button-wrapper .download_icon .mat-icon {
    vertical-align: middle;
    margin-right: 0.2em;
    background-color: #ce7a58;
    color: #fff;
    border-radius: 50%;
    padding: 0.2em;
    transform: scale(0.8);
}

::ng-deep .frm_tbl .btn_downloadForm .mat-button-wrapper span {
    align-self: center;
}

::ng-deep .frm_tbl .mat-row {
    border: none;
}

::ng-deep .frm_tbl .mat-row:nth-child(even) {
    background-color: #FAFAFA;
}

::ng-deep .frm_tbl .mat-row:nth-child(odd) {
    background-color: #fff;
}

::ng-deep .menuAction {
    font-weight: 500;
}

::ng-deep .menuAction .mat-icon {
    color: #ce7a58;
}

::ng-deep .mat-cell {
    min-height: unset !important;
}

@media screen and (max-width: 600px) {
    .frm_tbl .mat-header-row {
        display: none;
    }

    .frm_tbl .mat-table {
        border: 0;
        vertical-align: middle;
    }

    .frm_tbl .mat-table caption {
        font-size: 1em;
    }

    .frm_tbl .mat-table .mat-row {
        border-bottom: 5px solid #ddd;
        display: block;
        min-height: unset;
    }

    .frm_tbl .mat-table .mat-cell {
        min-height: unset !important;
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: 14px;
        text-align: right;
        margin-bottom: 4%;
        padding: 0 .5em;
    }

    .frm_tbl .mat-table .mat-cell:before {
        content: attr(data-label);
        float: left;
        font-weight: 500;
        font-size: 14px;
    }

    .frm_tbl .mat-table .mat-cell:last-child {
        border-bottom: 0;
    }

    .frm_tbl .mat-table .mat-cell:first-child {
        margin-top: 4%;
    }

    ::ng-deep .frm_tbl .mat-column-status {
        float: unset;
    }
    
    ::ng-deep .frm_tbl .mat-column-action {
        float: unset;
    }

    ::ng-deep .frm_tbl .mat-row:nth-child(even) {
        background-color: unset;
    }
    
    ::ng-deep .frm_tbl .mat-row:nth-child(odd) {
        background-color: unset;
    }
}

.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
}
