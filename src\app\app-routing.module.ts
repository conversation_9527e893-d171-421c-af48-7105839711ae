import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { environment } from 'env/environment';

const routes: Routes = [
  { path: '', loadChildren: () => import('modules/dashboard/dashboard.module').then(m => m.DashboardModule) },
  { path: 'form', loadChildren: () => import('modules/form/form.module').then(m => m.FormModule) },
  { path: 'statistics', loadChildren: () => import('modules/statistics/statistics.module').then(m => m.StatisticsModule) },
  { path: 'template', loadChildren: () => import('modules/template/template.module').then(m => m.TemplateModule) },
  { path: 'error', loadChildren: () => import('modules/error/error.module').then(m => m.ErrorModule) },
  { path: 'statistical', loadChildren: () => import('modules/statistical/statistical.module').then(m => m.StatisticalModule) },
  { path: '**', redirectTo: 'error/page-not-found' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, environment.routerConfig)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
