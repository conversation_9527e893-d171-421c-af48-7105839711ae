import java.text.SimpleDateFormat

def sdf = new SimpleDateFormat("yyyyMMddHHmmss")
def dateDefaultValue = sdf.format(new Date())

def version = env.BRANCH_NAME
def agentLabel = "211"

pipeline {
    agent { label agent<PERSON>abel }
    options { disableConcurrentBuilds() }
    environment {
        project = "images/web-reporter"
        nexus_registry = "idg-repository.tphcm.gov.vn"
        NEXUS = credentials('NEXUS')
		rancherUrl = "https://idg-platform.tphcm.gov.vn/p/local:p-fff2w/workload/deployment:hcm-common:common-web-reporter"
		namespace = "hcm-common"
		deployment = "common-web-reporter"
		container = "web-reporter"
    }
    stages {
        stage('Prepare') {
            steps {
				script {
                    version = version + "-" + dateDefaultValue
                }
                echo 'Prepare ... from ' + env.Server_IP + ' - build-numer: ' + env.BUILD_ID
                echo 'Prepare from source: ' + env.WORKSPACE
                echo 'Prepare version: ' + version
            }
        }
        stage('Build Docker images and Push to idg-repository') {
            steps {
                script {
                    echo '##### Build docker file for ' + env.BRANCH_NAME + ' branch.'
                    sh "/bin/bash ./charts/build_hcm.sh ${project} $BRANCH_NAME Dockerfile ${nexus_registry} ${NEXUS_USR} ${NEXUS_PSW} ${version}"
                }
            }
        }
		stage('Deploy image to Stagging site') {
            steps {
                script {
                    echo '##### Deploy image to idg-platform ' + env.BRANCH_NAME + ' branch.'
                    sh "/bin/bash /home/<USER>/lamtm/scriptIgateHCM/deploy_Stagging_IgateHCM.sh ${namespace} ${deployment} ${container} ${nexus_registry} ${project} ${version}"
                }
            }
        }
	}

	post {
		success {            
			withCredentials([string(credentialsId: 'TOKEN', variable: 'TOKEN_TELEGRAM'), string(credentialsId: 'CHAT_ID', variable: 'CHAT_ID_TELEGRAM'), usernamePassword(credentialsId: 'jenkinslog', passwordVariable: 'PASSWORD_JENKINS', usernameVariable: 'USER_JENKINS')]) {
				sh ("""
					curl -s -X POST "https://api.telegram.org/bot${TOKEN_TELEGRAM}/sendMessage?chat_id=${CHAT_ID_TELEGRAM}&parse_mode=html&text=+<b>Build</b>: <i>$BUILD_URL</i> Published is OK %0A+<b>Image</b>: <i>${nexus_registry}/${project}:${version}</i>  %0A+<b>Re-deploy project</b>: <i>${rancherUrl}</i>"
				""")
			}
		}
		aborted {     
			withCredentials([string(credentialsId: 'TOKEN', variable: 'TOKEN_TELEGRAM'), string(credentialsId: 'CHAT_ID', variable: 'CHAT_ID_TELEGRAM'), usernamePassword(credentialsId: 'jenkinslog', passwordVariable: 'PASSWORD_JENKINS', usernameVariable: 'USER_JENKINS')]) {
				sh ("""
					curl -s -X POST "https://api.telegram.org/bot${TOKEN_TELEGRAM}/sendMessage?chat_id=${CHAT_ID_TELEGRAM}&parse_mode=html&text=+<b>Build</b>: <i>$BUILD_URL</i> Published is Aborted"
				""")
			}
		}
		failure {  
			withCredentials([string(credentialsId: 'TOKEN', variable: 'TOKEN_TELEGRAM'), string(credentialsId: 'CHAT_ID', variable: 'CHAT_ID_TELEGRAM'), usernamePassword(credentialsId: 'jenkinslog', passwordVariable: 'PASSWORD_JENKINS', usernameVariable: 'USER_JENKINS')]) {
				sh ("""#!/bin/bash
					curl -s -S  -u $USER_JENKINS:$PASSWORD_JENKINS ${env.BUILD_URL}logText/progressiveText?start=0 -o ${project}_${env.BRANCH_NAME}.txt
					curl -s -X POST "https://api.telegram.org/bot${TOKEN_TELEGRAM}/sendMessage?chat_id=${CHAT_ID_TELEGRAM}&parse_mode=html&text=+<b>Build</b>: <i>$BUILD_URL</i> Published is Failure"
					curl -F document=@${project}_${env.BRANCH_NAME}.txt https://api.telegram.org/bot${TOKEN_TELEGRAM}/sendDocument?chat_id=${CHAT_ID_TELEGRAM}
				""")
			}
		}
	}
}
