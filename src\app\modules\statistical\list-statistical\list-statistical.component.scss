// ================================= searchForm
::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    color: transparent;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #dddddd;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
}

::ng-deep .frm_searchbar .searchForm .searchBtn {
    margin-top: 0.3em;
    background-color: #ce7a58;
    color: #fff;
    height: 3.2em;
}
::ng-deep .frm_searchbar .searchForm .downloadExcel {
    margin-top: 0.3em;
    background-color: #16a7eb;
    color: #fff;
    height: 3.2em;
}

::ng-deep .frm_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
}

::ng-deep .frm_searchbar .mat-form-field-label-wrapper {
    top: -1em;
}

::ng-deep .frm_searchbar .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
    font-size: 18px;
    margin-bottom: 1em;
}

::ng-deep .frm_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    color: #ce7a58;
    font-size: 18px;
    margin-bottom: 1em;
}
// ================================= table + frm_tbl + tab 1
::ng-deep .frm_tbl table {
    width: 100%;
}

.data-label {
    word-wrap: break-word;
}

::ng-deep .frm_tbl th.mat-header-cell, td.mat-cell {
    text-align: center;
    border: 1px solid #CCC;
    padding: 0 !important;
    word-wrap: break-word;
    color: #495057;
}

::ng-deep .frm_tbl .mat-header-row {
    background-color: #e8e8e8;
    word-wrap: break-word;
    // white-space: none;
    // white-space: normal;
}

::ng-deep .frm_tbl .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-row:nth-child(even) {
    background-color: #FAFAFA;
}

::ng-deep .frm_tbl .mat-row:nth-child(odd) {
    background-color: #fff;
}

// ==========================tab 1

::ng-deep .frm_tbl .mat-column-No1 {
    width: 4%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-No2 {
    width: 26%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-No3 {
    width: 24%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-No4 {
    width: 35%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-No5 {
    width: 24%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-Num1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-Num2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-Num3 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-Num4 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-Num5 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-Num6 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-Num7 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N1 {
    width: 4%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N2 {
    width: 26%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N3 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N4 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N5 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N6 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N7 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N8 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N9 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N10 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N11 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N12 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N13 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N14 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N15 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-N16 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-stt {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-sector {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-sum1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-acceptedOnl {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-pastDossier {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-acceptedDirect {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-sum2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-returnedOnTime {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-returnedOvertime {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-sum3 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-unresolvedNoTime {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-unresolvedOvertime {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-sum4 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-resolvedOnTime {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-resolvedOvertime {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl .mat-column-unresolved {
    width: 5%;
    word-wrap: break-word;
}
// ================================= tab 2
::ng-deep .frm_tbl1 table {
    width: 100%;
}

::ng-deep .frm_tbl1 th.mat-header-cell, td.mat-cell {
    text-align: center;
    border: 1px solid #CCC;
    padding: 0 !important;
    word-wrap: break-word;
    color: #495057;
}

::ng-deep .frm_tbl1 .mat-header-row {
    background-color: #e8e8e8;
    word-wrap: break-word;
    // white-space: none;
    // white-space: normal;
}

::ng-deep .frm_tbl1 .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-row:nth-child(even) {
    background-color: #FAFAFA;
}

::ng-deep .frm_tbl1 .mat-row:nth-child(odd) {
    background-color: #fff;
}

::ng-deep .frm_tbl1 .mat-column-No11 {
    width: 4%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-No12 {
    width: 26%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-No13 {
    width: 24%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-No14 {
    width: 35%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-No15 {
    width: 24%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-Num11 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-Num12 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-Num13 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-Num14 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-Num15 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-Num16 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-Num17 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K1 {
    width: 4%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K2 {
    width: 26%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K3 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K4 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K5 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K6 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K7 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K8 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K9 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K10 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K11 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K12 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K13 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K14 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K15 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-K16 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M11 {
    width: 4%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M12 {
    width: 26%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M13 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M14 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M15 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M16 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M17 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M18 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M19 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M110 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M111 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M112 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M113 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M114 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M115 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-M116 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-stt1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-sector1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-sum11 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-acceptedOnl1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-pastDossier1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-acceptedDirect1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-sum12 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-returnedOnTime1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-returnedOvertime1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-sum13 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-unresolvedNoTime1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-unresolvedOvertime1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-sum14 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-resolvedOnTime1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-resolvedOvertime1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-unresolved1 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .mat-column-agencyLevel {
    width: 100%;
    text-align: left !important;
    font-weight: bold;
    margin-left: 1em;
    word-wrap: break-word;
}

::ng-deep .frm_tbl1 .example-element-detail {
    overflow: hidden;
    display: flex;
}

::ng-deep .frm_tbl1 tr.example-element-row:not(.example-expanded-row):hover {
    background: #777;
}
  
::ng-deep .frm_tbl1  tr.example-element-row:not(.example-expanded-row):active {
    background: #efefef;
}
  
::ng-deep .frm_tbl1 .example-element-row td {
    border-bottom-width: 0;
}

::ng-deep .frm_tbl1 tr.example-detail-row {
    height: 0;
  }
// ================================= tab 3
::ng-deep .frm_tbl2 table {
    width: 100%;
}

::ng-deep .frm_tbl2 th.mat-header-cell, td.mat-cell {
    text-align: center;
    border: 1px solid #CCC;
    padding: 0 !important;
    word-wrap: break-word;
    color: #495057;
}

::ng-deep .frm_tbl2 .mat-header-row {
    background-color: #e8e8e8;
    word-wrap: break-word;
    // white-space: none;
    // white-space: normal;
}

::ng-deep .frm_tbl2 .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-row:nth-child(even) {
    background-color: #FAFAFA;
}

::ng-deep .frm_tbl2 .mat-row:nth-child(odd) {
    background-color: #fff;
}

::ng-deep .frm_tbl2 .mat-column-No21 {
    width: 4%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-No22 {
    width: 26%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-No23 {
    width: 24%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-No24 {
    width: 35%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-No25 {
    width: 24%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-Num21 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-Num22 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-Num23 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-Num24 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-Num25 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-Num26 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-Num27 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K11 {
    width: 4%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K12 {
    width: 26%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K13 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K14 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K15 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K16 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K17 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K18 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K19 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K110 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K111 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K112 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K113 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K114 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K115 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-K116 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M21 {
    width: 4%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M22 {
    width: 26%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M23 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M24 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M25 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M26 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M27 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M28 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M29 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M210 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M211 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M212 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M213 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M214 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M215 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-M216 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-stt2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .mat-column-sector2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-sum21 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-acceptedOnl2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-pastDossier2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-acceptedDirect2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-sum22 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-returnedOnTime2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-returnedOvertime2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-sum23 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-unresolvedNoTime2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-unresolvedOvertime2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-sum24 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-resolvedOnTime2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-resolvedOvertime2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tab2 .mat-column-unresolved2 {
    width: 5%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl2 .example-element-detailss {
    overflow: hidden;
    display: flex;
}

::ng-deep .frm_tbl2 tr.example-element-rowss:not(.example-expanded-rowss):hover {
    background: #777;
}
  
::ng-deep .frm_tbl2  tr.example-element-rowss:not(.example-expanded-rowss):active {
    background: #efefef;
}
  
::ng-deep .frm_tbl2 .example-element-rowss td {
    border-bottom-width: 0;
}

::ng-deep .frm_tbl2 tr.example-detail-rowss {
    height: 0;
}

::ng-deep .frm_tbl2 .agencytag {
    text-align: left !important;
    font-weight: bold;
    margin-left: 1em;
}

// ::ng-deep .frm_tbl2 tr.example-element-rowsss:not(.example-expanded-rowsss):hover {
//     background: #777;
// }
  
// ::ng-deep .frm_tbl2  tr.example-element-rowsss:not(.example-expanded-rowsss):active {
//     background: #efefef;
// }
  
// ::ng-deep .frm_tbl2 .example-element-rowsss td {
//     border-bottom-width: 0;
// }

// ::ng-deep .frm_tbl2 tr.example-detail-rowsss {
//     height: 0;
//   }
// ================================= tab 4
::ng-deep .frm_tbl3 table {
    width: 100%;
}

::ng-deep .frm_tbl3 th.mat-header-cell, td.mat-cell {
    text-align: center;
    border: 1px solid #CCC;
    padding: 0 !important;
    word-wrap: break-word;
    color: #495057;
}

::ng-deep .frm_tbl3 .mat-header-row {
    background-color: #e8e8e8;
    word-wrap: break-word;
    // white-space: none;
    // white-space: normal;
}

::ng-deep .frm_tbl3 .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-row:nth-child(even) {
    background-color: #FAFAFA;
}

::ng-deep .frm_tbl3 .mat-row:nth-child(odd) {
    background-color: #fff;
}

::ng-deep .frm_tbl3 .mat-column-K21 {
    width: 10%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-K22 {
    width: 30%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-K23 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-K24 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-K25 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-M31 {
    width: 10%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-M32 {
    width: 30%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-M33 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-M34 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-M35 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-stt3 {
    width: 10%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-sector3 {
    width: 30%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-sum3 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-cause3 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl3 .mat-column-note3 {
    width: 20%;
    word-wrap: break-word;
}

// ================================= tab 5
::ng-deep .frm_tbl4 table {
    width: 100%;
}

::ng-deep .frm_tbl4 th.mat-header-cell, td.mat-cell {
    text-align: center;
    border: 1px solid #CCC;
    padding: 0 !important;
    word-wrap: break-word;
    color: #495057;
}

::ng-deep .frm_tbl4 .mat-header-row {
    background-color: #e8e8e8;
    word-wrap: break-word;
    // white-space: none;
    // white-space: normal;
}

::ng-deep .frm_tbl4 .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-row:nth-child(even) {
    background-color: #FAFAFA;
}

::ng-deep .frm_tbl4 .mat-row:nth-child(odd) {
    background-color: #fff;
}

::ng-deep .frm_tbl4 .mat-column-K31 {
    width: 10%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-K32 {
    width: 30%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-K33 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-K34 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-M41 {
    width: 10%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-M42 {
    width: 30%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-M43 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-M44 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-stt4 {
    width: 10%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-procedure4 {
    width: 30%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-content4 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .mat-column-file4 {
    width: 20%;
    word-wrap: break-word;
}

::ng-deep .frm_tbl4 .example-element-details {
    overflow: hidden;
    display: flex;
}

::ng-deep .frm_tbl4 tr.example-element-rows:not(.example-expanded-rows):hover {
    background: #777;
}
  
::ng-deep .frm_tbl4  tr.example-element-rows:not(.example-expanded-rows):active {
    background: #efefef;
}
  
::ng-deep .frm_tbl4 .example-element-rows td {
    border-bottom-width: 0;
}

::ng-deep .frm_tbl4 tr.example-detail-rows {
    height: 0;
  }
// ================================= end tab
// ================================= frm_main
.frm_main {
    border-radius: 4px;
    padding: 1em;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);
}
