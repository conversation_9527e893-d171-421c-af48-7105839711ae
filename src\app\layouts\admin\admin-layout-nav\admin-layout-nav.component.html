<ngx-loading-bar [includeSpinner]="false" color="#CE7A58" height="3px"></ngx-loading-bar>
<div class="main-container" [class.main-is-mobile]="mobileQuery.matches">
    <mat-sidenav-container class="main-sidenav-container" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
        <mat-sidenav #snav [mode]="mobileQuery.matches ? 'over' : 'side'" [fixedInViewport]="mobileQuery.matches" [opened]="isSidebarActive()" fxFlex.gt-sm="20" fxFlex='70' [ngStyle]="{'background-image': 'url(' + sidenav.backgroundImg + ')'}">
            <div class="site_logo">
                <a href="/">
                    <div class="logo_img" fxShow="true" fxHide.lt-md [ngStyle]="{'background-image': 'url(' + siteContent.logoUrl + ')'}"></div>
                    <div class="site_name">
                        <span *ngIf="siteContent.name !== undefined">{{siteContent.name[selectedLang]}}</span>
                        <span>{{siteName}}</span>
                    </div>
                </a>
                <button class="close-button" mat-icon-button (click)="snav.toggle()" fxShow="true" fxHide.gt-sm>
                    <mat-icon>close</mat-icon>
                </button>
            </div>
            <!-- menu -->
            <mat-accordion [multi]="true">
                <span *ngFor='let m of sidebarMenu; let i = index;'>
                    <mat-divider *ngIf="i > 0"></mat-divider>
                    <mat-expansion-panel id="sidebar_menu" [expanded]="matchPosition(position, m.listSubMenu, i)"
                        *ngIf="countAvailableMenu[i] > 0">
                        <mat-expansion-panel-header [collapsedHeight]="'20px'" [expandedHeight]="'20px'">
                            <mat-panel-title>
                                <mat-icon class="material-icons-outlined"
                                    [ngClass]="{'menuIcon_active': m.active === true}">{{m.icon}} </mat-icon>
                                <span *ngFor="let mainmenu of m.mainMenu" [ngClass]="{'hidden': mainmenu.languageId != selectedLangId}">{{mainmenu.name}}</span>
                </mat-panel-title>
                </mat-expansion-panel-header>

                <span *ngFor='let submenu of m.listSubMenu; let j = index'>
                            <a id="submenu" mat-button *ngIf="submenu.isActive"
                                routerLinkActive="active" routerLink="/{{ submenu.route }}">
                                <span class="submenuTitle" *ngFor="let sub of submenu.title" [ngClass]="{'hidden': sub.languageId != selectedLangId}">{{sub.name}}</span>
                </a>
                </span>
                </mat-expansion-panel>
                </span>
            </mat-accordion>
        </mat-sidenav>
        <mat-sidenav-content fxFlex.gt-sm="80" fxFlex='100'>
            <mat-toolbar *ngIf="isEnableToolbar()" class="main-toolbar">
                <!-- Responsive -->
                <mat-toolbar-row fxShow="true" fxHide.gt-sm>
                    <button class="btn_toggleMenu" mat-icon-button (click)="snav.toggle()">
                        <mat-icon>menu_open</mat-icon>
                    </button>

                    <span class="toolbar-spacer"></span>
                    <a href="/" class="site_logo">
                        <div class="logo_img" [ngStyle]="{'background-image': 'url(' + config.cloudStaticURL + 'logo/quoc-huy.png)'}"></div>
                    </a>

                    <span class="toolbar-spacer"></span>

                    <button mat-icon-button [matMenuTriggerFor]="resMenu" class="btn_resMenu">
                        <mat-icon>category</mat-icon>
                    </button>
                    <mat-menu #resMenu="matMenu">
                        <button mat-menu-item [matMenuTriggerFor]="gridMenu" *ngIf="listAppEnable == 1 && listApps.length > 0">
                            <mat-icon>apps</mat-icon>
                            <span i18n>Ứng dụng</span>
                        </button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item [matMenuTriggerFor]="onegateMenu" *ngIf="listAppEnable == 1 && listOnegateApps.length > 0">
                            <mat-icon class="material-icons-outlined">widgets</mat-icon>
                            <span i18n>Ứng dụng một cửa</span>
                        </button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item>
                            <mat-icon>account_balance</mat-icon>
                            {{siteName}}
                        </button>
                        <button mat-menu-item [matMenuTriggerFor]="langMenu" *ngIf="selectedLang == 'vi'">
                            <mat-icon>language</mat-icon>
                            Tiếng Việt (VI)
                        </button>
                        <button mat-menu-item [matMenuTriggerFor]="langMenu" *ngIf="selectedLang == 'en'">
                            <mat-icon>language</mat-icon>
                            English (EN)
                        </button>
                        <button mat-menu-item [matMenuTriggerFor]="notiMenu" *ngIf="notificationEnable == 1">
                            <mat-icon matBadgeSize="small" matBadge="{{totalDossierRemind}}" matBadgeColor="warn">notifications</mat-icon>
                            <span i18n>Thông báo</span>
                        </button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item [matMenuTriggerFor]="accountMenu">
                            <mat-icon>account_circle</mat-icon>
                            <span class="res_accountName">{{userName}}</span>
                        </button>
                    </mat-menu>
                </mat-toolbar-row>

                <mat-toolbar-row fxShow="true" fxHide.lt-md>
                    <!-- Show/hide sidebar -->
                    <button class="btn_toggleMenu" mat-icon-button (click)="snav.toggle()">
                        <mat-icon>menu_open</mat-icon>
                    </button>
                    <ng-container *ngIf="userAgency.length < 2">
                        <a mat-button class="main-app-name">
                            <h1 *ngIf="agencyName == ''">{{siteName}}</h1>
                            <h1 *ngIf="agencyName != ''">{{agencyName}}</h1>
                        </a>
                    </ng-container>
                    <ng-container *ngIf="userAgency.length > 1">
                        <mat-select [(value)]="selectedOption" style="max-width: 20em;">
                            <mat-option *ngFor="let ag of userAgency" value="{{ag.agency.id}}" (click)='onAgencyChange(ag.agency)' style="font-size: 16px;line-height: 3em;height: 3em;">
                                {{ ag.agency.name }}
                            </mat-option>
                        </mat-select>
                    </ng-container>

                    <span class="toolbar-spacer"></span>

                    <a mat-button class="btn_gridMenu" *ngIf="homeUrl != null && homeUrl != ''" href="{{homeUrl}}">
                        <mat-icon>home</mat-icon>
                    </a>
                    <mat-divider vertical class="v_divider"></mat-divider>

                    <!-- Noti -->
                    <a mat-button [matMenuTriggerFor]="notiMenu" class="btn_notiMenu" *ngIf="notificationEnable == 1">
                        <ng-container *ngIf="listDossierRemind.length > 0">
                            <mat-icon class="material-icons-outlined" matBadge="{{totalDossierRemind}}" matBadgeColor="warn">notifications</mat-icon>
                        </ng-container>
                        <ng-container *ngIf="listDossierRemind.length === 0">
                            <mat-icon class="material-icons-outlined">notifications</mat-icon>
                        </ng-container>
                    </a>
                    <mat-menu #notiMenu="matMenu">
                        <ng-container *ngIf="listDossierRemind.length > 0">
                            <ng-container *ngFor="let item of listDossierRemind">
                                <a (click)='onClickRemind(item.id)' class="remindMenu" mat-menu-item>
                                    <span class="remindNumber">{{item.count}}</span>
                                    <span class="remindName" *ngIf="item.name != null">{{item.name}}</span>
                                    <span class="remindName" *ngIf="item.name == null">(Công việc chưa cấu hình tên)</span>
                                </a>
                            </ng-container>
                        </ng-container>
                        <ng-container *ngIf="listDossierRemind.length === 0">
                            <div mat-menu-item i18n>Không có thông báo nào!</div>
                        </ng-container>
                    </mat-menu>

                    <mat-divider vertical class="v_divider"></mat-divider>

                    <!-- onegateMenu -->
                    <a mat-button [matMenuTriggerFor]="onegateMenu" class="btn_gridMenu" *ngIf="listAppEnable == 1 && listOnegateApps.length > 0">
                        <mat-icon class="material-icons-outlined">widgets</mat-icon>
                    </a>
                    <mat-menu #onegateMenu="matMenu" class="menu_apps">
                        <div class="gridApps">
                            <a class="items" *ngFor="let app of listOnegateApps" matTooltip="{{app.app?.name}}" href="{{app.appDeployment?.configuration?.url}}" target="blank">
                                <div class="appLogo" [style.background-image]="app.app?.logoURL"></div>
                                <span class="appName">
                                    {{app.app?.name}}
                                    <span *ngIf="app.app?.name == undefined || app.app?.name == null || app.app?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </span>
                            </a>
                        </div>
                    </mat-menu>
                    <mat-divider vertical class="v_divider" *ngIf="listAppEnable == 1 && listOnegateApps.length > 0"></mat-divider>
                    <!-- gridMenu -->
                    <a mat-button [matMenuTriggerFor]="gridMenu" class="btn_gridMenu" *ngIf="listAppEnable == 1 && listApps.length > 0">
                        <mat-icon>apps</mat-icon>
                    </a>
                    <mat-menu #gridMenu="matMenu" class="menu_apps">
                        <div class="gridApps">
                            <a class="items" *ngFor="let app of listApps" matTooltip="{{app.app?.name}}" href="{{app.appDeployment?.configuration?.url}}" target="blank">
                                <div class="appLogo" [style.background-image]="app.app?.logoURL"></div>
                                <span class="appName" *ngIf="app.app.brandname != '' && app.app.brandname != null">
                                    {{app.app?.brandname}}
                                    <!-- <span *ngIf="app.app?.name == undefined || app.app?.name == null || app.app?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span> -->
                                </span>
                                <span class="appName" *ngIf="app.app.brandname == '' || app.app.brandname == null">
                                    {{app.app?.name}}
                                    <span *ngIf="app.app?.name == undefined || app.app?.name == null || app.app?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </span>
                            </a>
                        </div>
                    </mat-menu>

                    <mat-divider vertical class="v_divider" *ngIf="listAppEnable == 1 && listApps.length > 0"></mat-divider>

                    <!-- Account -->
                    <a mat-button class="btn_accountMenu" [matMenuTriggerFor]="accountMenu">
                        <span id="account_name">
                            <div class="avatar" [style.background-image]="avatar"></div>
                            {{userName}}
                        </span>
                    </a>
                    <mat-menu #accountMenu="matMenu">
                        <a mat-menu-item target="blank" href="{{config.webAccountURL}}my-account/info">
                            <mat-icon>account_circle</mat-icon><span>Tài khoản</span>
                        </a>
                        <button mat-menu-item (click)="logout()">
                            <mat-icon>exit_to_app</mat-icon><span>Đăng xuất</span>
                        </button>
                    </mat-menu>
                    <mat-divider vertical class="v_divider"></mat-divider>

                    <!-- Lang -->
                    <button mat-button [matMenuTriggerFor]="langMenu" align="end" class="btn_langMenu" *ngIf="selectedLang == 'vi'">
                        <img src="{{config.cloudStaticURL}}logo/flag/vi.jpg" id="lang_flag" alt=""
                            height="18em">
                        <span id="lang_acronym">Tiếng Việt</span>
                        <mat-icon>expand_more</mat-icon>
                    </button>
                    <button mat-button [matMenuTriggerFor]="langMenu" align="end" class="btn_langMenu" *ngIf="selectedLang == 'en'">
                        <img src="{{config.cloudStaticURL}}logo/flag/en.jpg" id="lang_flag" alt=""
                            height="18em">
                        <span id="lang_acronym">English</span>
                        <mat-icon>expand_more</mat-icon>
                    </button>
                    <mat-menu #langMenu="matMenu">
                        <button mat-menu-item (click)="changeLanguage('vi', 228)">
                            <button mat-button disabled>
                                <img src="{{config.cloudStaticURL}}logo/flag/vi.jpg" id="lang_flag" alt=""
                                    height="20em">
                                <span id="lang_acronym">Tiếng Việt (VI)</span>
                            </button>
                        </button>
                        <button mat-menu-item (click)="changeLanguage('en', 46)">
                            <button mat-button disabled>
                                <img src="{{config.cloudStaticURL}}logo/flag/en.jpg" id="lang_flag" alt=""
                                    height="20em">
                                <span id="lang_acronym">English (EN)</span>
                            </button>
                        </button>
                    </mat-menu>
                </mat-toolbar-row>
            </mat-toolbar>
            <div class="content">
                <router-outlet></router-outlet>
            </div>
            <mat-toolbar class="footer" fxShow="true" fxHide.lt-md>
                <div class="copyright">
                    <!-- <mat-icon>copyright</mat-icon> -->
                    <div *ngIf="footerContent.name != undefined" class="itemRight">{{footerContent.name[selectedLang]}}</div>
                    <div *ngIf="footerContent.address != undefined" class="itemRight">{{footerContent.address[selectedLang]}}</div>
                </div>
                <span class="toolbar-spacer"></span>
                <div>
                    <div class="developedBy">
                        <span>{{footerContent.right.name[selectedLang]}}</span>
                        <div class="img" [ngStyle]="{'background-image': 'url(' + footerContent.right.logoUrl + ')'}"></div>
                    </div>
                    <div class="developedBy">
                        <span>{{footerContent.right.version[selectedLang]}}</span>
                    </div>
                </div>
            </mat-toolbar>
        </mat-sidenav-content>
    </mat-sidenav-container>
</div>