import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import {MatExpansionModule} from '@angular/material/expansion';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import { AdminLayoutModule } from 'src/app/layouts/admin/admin-layout.module';
import { SharedModule } from 'src/app/shared/shared.module';

import { ListStatisticalComponent } from './list-statistical/list-statistical.component';
import { StatisticalRoutingModule } from './statistical-routing.module';

@NgModule({
  declarations: [ListStatisticalComponent],
  imports: [
    CommonModule,
    StatisticalRoutingModule,
    AdminLayoutModule,
    MatExpansionModule,
    SharedModule,
    MatButtonModule,
    MatIconModule,
  ]
})
export class StatisticalModule { }
