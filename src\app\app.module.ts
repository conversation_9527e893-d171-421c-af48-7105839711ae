import { BrowserModule } from '@angular/platform-browser';
import { NgModule, APP_INITIALIZER } from '@angular/core';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CoreModule } from './core/core.module';
import { SharedModule } from './shared/shared.module';
import { KeycloakService, KeycloakAngularModule } from 'keycloak-angular';
import { initializer } from './app-init';
import { EnvService } from './core/service/env.service';
import { FlexLayoutModule } from '@angular/flex-layout';
import { DatePipe } from '@angular/common'
import {DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE} from '@angular/material/core';
import * as XLSX from 'xlsx';
import {NgxPrintModule} from 'ngx-print';
import { PrintService } from 'src/app/data/service/print/print.service';

@NgModule({
  declarations: [
    AppComponent,
  ],
  imports: [
    BrowserModule,
    KeycloakAngularModule,
    CoreModule,
    SharedModule,
    AppRoutingModule,
    BrowserAnimationsModule,
	FlexLayoutModule,
    NgxPrintModule
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: initializer,
      multi: true,
      deps: [KeycloakService, EnvService]
    },
    DatePipe,
    {provide: MAT_DATE_LOCALE, useValue: 'en-GB'},
    PrintService

  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
