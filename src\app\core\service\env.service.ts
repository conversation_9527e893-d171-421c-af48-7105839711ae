import { Injectable } from '@angular/core';
import { environment } from 'env/environment';
import { HttpClient, HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class EnvService {

  private env;

  constructor(private httpClient: HttpClient) {
    this.env = environment;
  }

  getEnv() {
    return this.env;
  }

  getConfig() {
    return this.env.config;
  }

  getTranslateNotificationLabel(lang, status, method) {
    return this.env.config.translateNotificationLabel[lang][status][method];
  }


  loadConfig(): Promise<any> {
    const inst = this;
    return new Promise<any>((resolve, reject) => {
      if (inst.env.loadConfigFromUrl && inst.env.configUrl) {
        inst.httpClient.get(inst.env.configUrl, {
          responseType: 'json',
          params: new HttpParams().set('t', new Date().getTime().toString())
        }).toPromise().then((config) => {
          inst.env.config = { ...inst.env.config, ...config };
          resolve(inst.env.config);
        });
      } else {
        resolve(inst.env.config);
      }
    });
  }

}


