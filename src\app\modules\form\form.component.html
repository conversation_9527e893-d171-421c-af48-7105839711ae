<h2><PERSON><PERSON> mục gi<PERSON>y tờ</h2>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_searchbar" fxFlex="grow">
        <form [formGroup]="searchForm" (submit)="onConfirmSearch()" class="searchForm">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
                <mat-form-field appearance="outline" fxFlex.gt-sm="82" fxFlex='79'>
                    <mat-label>Nhập từ khoá</mat-label>
                    <input matInput formControlName="keyword">
                </mat-form-field>
                <div fxFlex='1'></div>
                <button mat-flat-button fxFlex.gt-sm="17" fxFlex='20' class="searchBtn" type="submit">
                    <mat-icon>search</mat-icon> Tìm kiếm
                </button>
            </div>
        </form>
    </div>
</div>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <button mat-flat-button class="btn_add" (click)="addFormDialog()">
            <mat-icon>add</mat-icon>
            Thêm mới
        </button>
        <div class="frm_tbl">
            <table mat-table
                [dataSource]="dataSource">
                <ng-container matColumnDef="stt">
                    <mat-header-cell *matHeaderCellDef> STT </mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="STT"> {{row.stt}} </mat-cell>
                </ng-container>

                <ng-container matColumnDef="code">
                    <mat-header-cell *matHeaderCellDef> Tên tắt giấy tờ </mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Tên tắt giấy tờ" class="cell_code"> {{row.code}}
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="name">
                    <mat-header-cell *matHeaderCellDef> Tên giấy tờ </mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Tên giấy tờ"> {{row.name}}
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="status">
                    <mat-header-cell *matHeaderCellDef> Trạng thái </mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Trạng thái"> {{getStatus(row.status)}}
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="action">
                    <mat-header-cell *matHeaderCellDef> Thao tác </mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Thao tác">
                        <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                            <mat-icon>more_horiz</mat-icon>
                        </button>
                        <mat-menu #actionMenu="matMenu" xPosition="before">
                            <button mat-menu-item class="menuAction" (click)="updateFormDialog(row.id)">
                                <mat-icon>edit</mat-icon> Cập nhật
                            </button>
                            <button mat-menu-item class="menuAction" (click)="deleteFormDialog(row.id, row.name)">
                                <mat-icon>delete_outline</mat-icon> Xoá
                            </button>
                        </mat-menu>
                    </mat-cell>
                </ng-container>

                <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </table>
            <div class="frm_Pagination">
                <ul class="temp_Arr">
                    <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}"></li>
                </ul>
                <div class="pageSize">
                    <span>Hiển thị </span>
                    <mat-form-field appearance="outline">
                        <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                            <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <span>trên {{countResult}} bản ghi</span>
                </div>
                <div class="control">
                    <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true"
                        previousLabel="" nextLabel="">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
</div>