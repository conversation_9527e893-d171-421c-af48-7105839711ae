#!/bin/bash
# version 2021.04.26
# /bin/bash build.sh <project> <branch> <Dockerfile> <nexus_registry> <NEXUS_USR> <NEXUS_PSW> [version]
# sh build.sh IT.KV2.GP2.ORIMX-Search master Dockerfile crelease.devops.vnpt.vn:10108 auto.kv2.it xxx 2.0.1
#					1					2		3						4					5		6	7

project_key=""
project=""                              # project-name
tag=""                                  # docker image tag (branch)
Dockerfile="Dockerfile" # Dockerfile
registry=""
registry_user=""
registry_password=""

# Check parameter project and branch
if [ ! -z "$2" ]; then
    # project="$(echo $1 | tr '[A-Z]' '[a-z]')"
    project=$(echo $1 | tr [:upper:] [:lower:])
    # echo $project
    if [ ! -z "$7" ]; then
        tag="$7"
    elif [ $2 = "master" ]; then
        tag="stable"
    elif [ $2 = "pro" ] || [ $2 = "prod" ]  || [ $2 = "release" ]; then
        tag="latest"
    else
        tag="$2"
    fi
else
    echo "Can not build empty tag for project $2"
    exit 1
fi

# Check parameter Dockerfile
if [ ! -z "$3" ]; then
    Dockerfile="$3"
fi
# Check parameter Registry
if [ ! -z "$4" ]; then
    registry="$4"
fi
if [ ! -z "$5" ]; then
        registry_user="$5"
fi
if [ ! -z "$6" ]; then
    registry_password="$6"
fi

# Check Dockerfile exits
if [ -f "./Dockerfile" ]; then
    echo "$(date +'%d-%m-%Y %H:%M:%S') [$HOSTNAME] Build $Dockerfile"
	#cd $project_key
	npm install
	npm run build-i18n
    docker build --no-cache -f $Dockerfile -t $registry/$project:$tag .
else
    echo "$Dockerfile not found!"
    exit 1
fi
retVal=$?
if [ $retVal -gt 0 ]; then
    echo "Build failed - exit code: $retVal"
    exit 1
fi

# Push to local registry
if [ ! -z "$registry" ] && [ ! -z "$registry_user" ] && [ ! -z "$registry_password" ]; then
    echo "Push $registry/$project_key:$tag to local Docker Registry: $registry"
    #docker login $registry -u $registry_user -p $registry_password
    # docker tag $project:$tag $registry/$project_key/$project:$tag
    docker push $registry/$project:$tag
    # Remove Docker Imgae
    # docker image rm $project:$tag
    docker rmi $registry/$project:$tag
    # docker system prune -f
else
        echo "Local Registry not set. Skip push to local Registry!"
fi

echo "$(date +'%d-%m-%Y %H:%M:%S') [$HOSTNAME] End build"
