.breadcrumb {
  padding: 10px 0;

  a {
    color: #007bff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  mat-icon {
    margin: 0 5px;
    color: #6c757d;
  }
}

.frm_searchbar {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;

  .searchForm {
    mat-form-field {
      margin-right: 15px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .btn-search-modify {
    background-color: #007bff;
    color: white;
    height: 56px;

    .iconStatistical {
      margin-right: 8px;
    }

    &:disabled {
      background-color: #6c757d;
    }
  }
}

.frm_tbl0 {
  position: relative;

  .overload {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .loading {
      .spinner-3 {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  table {
    width: 100%;

    .cell_info {
      text-align: center;
      padding: 8px;

      a {
        color: #333;
        text-decoration: none;
      }
    }
  }
}

.st_tabs {
  .mat-tab-label {
    min-width: 120px;
  }
}

.listAgency {
  margin: 20px 0;

  .st_item {
    margin: 10px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;

    .head {
      text-align: center;
      margin-bottom: 15px;

      .lableChart {
        font-weight: bold;
        margin: 5px 0;
        color: #495057;
      }
    }

    .body {
      canvas {
        max-height: 400px;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .frm_searchbar {
    .searchForm {
      mat-form-field {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
      }
    }
  }

  .listAgency {
    .st_item {
      margin: 5px 0;
    }
  }
}