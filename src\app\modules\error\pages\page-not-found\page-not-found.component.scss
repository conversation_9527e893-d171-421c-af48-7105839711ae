@import url("https://fonts.googleapis.com/css2?family=Concert+One&display=swap");

@keyframes move_wave {
    0% {
        transform: translateX(0) translateZ(0) scaleY(1);
    }
    50% {
        transform: translateX(-25%) translateZ(0) scaleY(0.55);
    }
    100% {
        transform: translateX(-50%) translateZ(0) scaleY(1);
    }
}

.content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 16;
    text-align: center;
}

.content h1 {
    color: #fff;
    margin-top: 1em;
}

.content button {
    background-color: rgba(255,255,255,.12);
    color: #fff;
}

.waveWrapper {
    overflow: hidden;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    margin: auto;
}
.waveWrapperInner {
    position: absolute;
    width: 100%;
    overflow: hidden;
    height: 100%;
    background-image: linear-gradient(to top, #3f51b5 20%, #27273c 80%);
}
.bgTop {
    z-index: 15;
    opacity: 0.5;
}
.bgMiddle {
    z-index: 10;
    opacity: 0.75;
}
.bgBottom {
    z-index: 5;
}
.wave {
    position: absolute;
    left: 0;
    width: 200%;
    height: 100%;
    background-repeat: repeat no-repeat;
    background-position: 0 bottom;
    transform-origin: center bottom;
}
.waveTop {
    background-size: 50% 100px;
}
.waveAnimation .waveTop {
    animation: move-wave 3s;
    -webkit-animation: move-wave 3s;
    -webkit-animation-delay: 1s;
    animation-delay: 1s;
}
.waveMiddle {
    background-size: 50% 120px;
}
.waveAnimation .waveMiddle {
    animation: move_wave 10s linear infinite;
}
.waveBottom {
    background-size: 50% 100px;
}
.waveAnimation .waveBottom {
    animation: move_wave 15s linear infinite;
}

.animation-container {
    font-weight: 800;
    font-family: "Concert One", cursive;
    color: #fff;
    font-size: 50vh;
    opacity: 1;
}

.animation-container svg {
    width: 90%;
    display: block;
    margin: 0 0 0 5%;
}

.animation-container svg .text {
    width: 100%;
    fill: transparent;
    stroke-width: 1;
    stroke-linejoin: round;
    stroke-dasharray: 90, 310;
    stroke-dashoffset: 0;
    -webkit-animation: text 8s infinite linear;
    animation: text 8s infinite linear;
}

.animation-container svg .text:nth-child(4n + 1) {
    stroke: #fff;
    -webkit-animation-delay: -2s;
    animation-delay: -2s;
}

.animation-container svg .text:nth-child(4n + 2) {
    stroke: #fff;
    -webkit-animation-delay: -4s;
    animation-delay: -4s;
}

.animation-container svg .text:nth-child(4n + 3) {
    stroke: #fff;
    -webkit-animation-delay: -6s;
    animation-delay: -6s;
}

.animation-container svg .text:nth-child(4n + 4) {
    stroke: #fff;
    -webkit-animation-delay: -8s;
    animation-delay: -8s;
}

@-webkit-keyframes text {
    100% {
        stroke-dashoffset: -400;
    }
}
