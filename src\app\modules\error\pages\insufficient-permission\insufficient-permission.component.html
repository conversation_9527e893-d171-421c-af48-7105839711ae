<div class="waveWrapper waveAnimation">
    <div class="content">
        <div class="animation-container">
            <svg viewBox="0 -150 600 600">
                <symbol id="text">
                    <text text-anchor="middle" x="50%" y="50%">403</text>
                </symbol>
                <use xlink:href="#text" class="text"></use>
                <use xlink:href="#text" class="text"></use>
                <use xlink:href="#text" class="text"></use>
                <use xlink:href="#text" class="text"></use>
            </svg>
        </div>
        <h1><span>Sorry, Permission denied!</span></h1>
        <a href="/"><button mat-flat-button class="mr-1" >Back to Home Page</button></a>
        <button (click)="logout()" mat-flat-button class="ml-1" >Logout</button>
    </div>
    <div class="waveWrapperInner bgTop">
        <div class="wave waveTop" style="background-image: url('https://staticcloud.vncitizens.vn/img/wave-top.png')">
        </div>
    </div>
    <div class="waveWrapperInner bgMiddle">
        <div class="wave waveMiddle" style="background-image: url('https://staticcloud.vncitizens.vn/img/wave-mid.png')">
        </div>
    </div>
    <div class="waveWrapperInner bgBottom">
        <div class="wave waveBottom" style="background-image: url('https://staticcloud.vncitizens.vn/img/wave-bot.png')">
        </div>
    </div>
</div>