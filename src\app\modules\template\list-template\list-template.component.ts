import { Component, OnInit, ChangeDetectorRef, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TemplateService } from 'src/app/data/service/template/template.service';
import { EnvService } from 'src/app/core/service/env.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';
import { ConfirmSearchDialogModel, AddTemplateComponent } from '../dialogs/add-template/add-template.component';
import { ConfirmDeleteDialogModel, DeleteTemplateComponent } from '../dialogs/delete-template/delete-template.component';
import { ConfirmUpdateDialogModel, UpdateTemplateComponent } from '../dialogs/update-template/update-template.component';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
@Component({
  selector: 'app-list-template',
  templateUrl: './list-template.component.html',
  styleUrls: ['./list-template.component.scss', '../../../app.component.scss']
})

export class ListTemplateComponent implements OnInit, AfterViewInit {

  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  countResult = 0;
  size = 10;
  page = 1;
  pageIndex = 1;
  pgSizeOptions = this.config.pageSizeOptions;
  paramsQuery = {
    keyword: '',
    page: '1',
    size: '10',
    subsystem: '',
    templateType: ''
  };

  searchForm = new FormGroup({
    keyword: new FormControl(''),
    subsystem: new FormControl(''),
    templateType: new FormControl(''),
  });

  listSubsystem = [];

  // Search TemplateType
  keywordTemplateType = '';
  totalPagesTemplateType = 0;
  currentPageTemplateType = 0;
  pageSizeTemplateType = 10;
  timeOutTemplateType: any = null;
  listTemplateType: Array<any> = [];

  displayedColumns: string[] = ['stt', 'name', 'subsystem', 'typeName', 'fileName', 'action'];
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;

  constructor(
    private router: Router,
    private dialog: MatDialog,
    private templateService: TemplateService,
    private envService: EnvService,
    private cdRef: ChangeDetectorRef,
    private snackbarService: SnackbarService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }

  ngOnInit(): void {
    const searchString = '?page=0&size=10&spec=page&keyword=';
    this.getListSubsystem();
    this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
    this.getListTemplate(searchString);
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  // Search Function
  getNextBatch(type) {
    switch (type) {
      case 'templateType': {
        this.currentPageTemplateType += 1;
        this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
        break;
      }
    }
  }

  onEnter(type, event) {
    switch (type) {
      case 'templateType': {
        clearTimeout(this.timeOutTemplateType);
        this.timeOutTemplateType = setTimeout(async () => {
          this.keywordTemplateType = event.target.value;
          this.currentPageTemplateType = 0;
          this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
        }, 300);
        break;
      }
    }
  }

  async resetSearchForm(type) {
    switch (type) {
      case 'templateType': {
        this.currentPageTemplateType = 0;
        this.keywordTemplateType = '';
        this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
        break;
      }
    }
  }

  onConfirm() {
    const formObj = this.searchForm.getRawValue();
    const searchString = this.generateSearchString('page', 0, this.size, '');
    this.paramsQuery = {
      keyword: formObj.keyword,
      page: '1',
      size: this.size.toString(),
      subsystem: formObj.subsystem,
      templateType: formObj.templateType
    };
    this.pageIndex = 1;
    this.page = 1;

    this.router.navigate([], {
      queryParams: {
        keyword: formObj.keyword,
        page: '1',
        size: this.size.toString(),
        subsystem: formObj.subsystem,
        'template-type': formObj.templateType
      }
    });
    this.getListTemplate(searchString);
  }

  generateSearchString(spec, page, size, sort) {
    const formObj = this.searchForm.getRawValue();
    return '?spec=' + spec +
      '&page=' + page +
      '&size=' + size +
      '&sort=' + sort +
      '&keyword=' + encodeURI(formObj.keyword.trim()) +
      '&subsystem-id=' + formObj.subsystem +
      '&type-id=' + formObj.templateType;
  }

  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size, '');
        this.getListTemplate(searchString);
        this.router.navigate([], {
          queryParams: {
            keyword: this.paramsQuery.keyword,
            page: this.pageIndex,
            size: this.size,
            subsystem: this.paramsQuery.subsystem,
            'template-type': this.paramsQuery.templateType
          }
        });
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        const searchString2 = this.generateSearchString('page', (this.pageIndex - 1), this.size, '');
        this.getListTemplate(searchString2);
        this.router.navigate([], {
          queryParams: {
            keyword: this.paramsQuery.keyword,
            page: 1,
            size: this.size,
            subsystem: this.paramsQuery.subsystem,
            'template-type': this.paramsQuery.templateType
          }
        });
        break;
    }
  }

  addTemplate() {
    const dialogData = new ConfirmSearchDialogModel();
    const dialogRef = this.dialog.open(AddTemplateComponent, {
      minWidth: '60vw',
      maxWidth: '60vw',
      maxHeight: '80vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      const content = '';
      if (res.status === true) {
        const totalPage = Math.ceil((this.countResult + 1) / this.size);
        this.page = totalPage;
        this.paginate(totalPage, 1);
        this.snackbarService.openSnackBar(1, this.envService.getTranslateNotificationLabel(this.selectedLang, 'success', 'post'), content, 'success_notification', this.config.expiredTime);
      }
      if (res.status === false) {
        if (res.errStatus === 409) {
          const msgObj = {
            vi: 'Lý do: Tên phiếu động đã bị trùng!',
            en: 'Reason: Duplicate name!'
          };
          // tslint:disable-next-line: max-line-length
          this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'post'), msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
        } else {
          this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'post'), content, 'error_notification', this.config.expiredTime);
        }
      }
    });
  }

  deleteTemplateDialog(id, name, filepath) {
    const dialogData = new ConfirmDeleteDialogModel(id, name, filepath);
    const dialogRef = this.dialog.open(DeleteTemplateComponent, {
      minWidth: '50vw',
      maxWidth: '50vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    const content = '';
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res === true) {
        const totalPage = Math.ceil((this.countResult - 1) / this.size);
        if (this.page > totalPage) {
          this.page = totalPage;
          this.paginate(totalPage, 0);
        }
        else { this.paginate(this.page, 0); }
        this.snackbarService.openSnackBar(1, this.envService.getTranslateNotificationLabel(this.selectedLang, 'success', 'delete'), content, 'success_notification', this.config.expiredTime);
      }
      if (res === false) {
        this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'delete'), content, 'error_notification', this.config.expiredTime);
      }
    });
  }

  updateTemplateDialog(id) {
    const dialogData = new ConfirmUpdateDialogModel(id);
    const dialogRef = this.dialog.open(UpdateTemplateComponent, {
      minWidth: '60vw',
      maxWidth: '60vw',
      maxHeight: '80vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      const content = '';
      if (res?.status === true) {
        this.snackbarService.openSnackBar(1, this.envService.getTranslateNotificationLabel(this.selectedLang, 'success', 'put'), content, 'success_notification', this.config.expiredTime);
        this.paginate(this.pageIndex, 0);
      }
      if (res?.status === false) {
        this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'put'), content, 'error_notification', this.config.expiredTime);
      }
    });
  }

  downloadFile(idTemplate, filename) {
    this.templateService.downloadFile(idTemplate).subscribe(data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
      downloadLink.setAttribute('download', filename);
      document.body.appendChild(downloadLink);
      downloadLink.click();
    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy file!',
        en: 'File not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }
  // ========================================================== Manual function

  getLanguageId(selectedLang) {
    if (selectedLang === 'vi') {
      return 228;
    } else {
      return 46;
    }
  }

  getListSubsystem() {
    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < this.config.listSubsystem.length; i++) {
      const subsystem = {
        id: this.config.listSubsystem[i].id,
        code: this.config.listSubsystem[i].code,
        name: ''
      };
      // tslint:disable-next-line: prefer-for-of
      for (let j = 0; j < this.config.listSubsystem[i].name.length; j++) {
        if (Number(this.config.listSubsystem[i].name[j].languageId) === this.getLanguageId(this.selectedLang)) {
          subsystem.name = this.config.listSubsystem[i].name[j].name;
        }
      }
      this.listSubsystem.push(subsystem);
    }
    console.log(this.listSubsystem);
  }

  getListTemplate(searchString) {
    this.templateService.getListTemplate(searchString, 'createdDate,desc').subscribe(async data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        // tslint:disable-next-line: prefer-for-of
        const type = data.content[i].type;

        if(!type.name){
          data.content[i].typeName = '';
        }else{
          for (let j = 0; j < type.name.length; j++) {
            if (this.getLanguageId(this.selectedLang) === data.content[i].type.name[j].languageId) {
              data.content[i].typeName = data.content[i].type.name[j].name;
            }
          }
        }
        

        if (data.content[i].subsystem !== null) {
          let product = '';
          const subsystem = data.content[i].subsystem;

          // tslint:disable-next-line: prefer-for-of
          for (let j = 0; j < subsystem.length; j++) {
            for await (const names of subsystem[j].name) {
              if (this.getLanguageId(this.selectedLang) === Number(names.languageId)) {
                product = product + names.name + ', ';
              }
            }
          }
          data.content[i].subsystem = product.substring(0, product.length - 2);
        }

        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  getListTemplateType(keyword, page, size) {
    const searchString = '--by-category-id?category-id=5f5b27924e1bd312a6f3ae1e&keyword=' + keyword + '&page=' + page + '&size=' + size + '&spec=page&sort=order';

    this.templateService.getListCategory(searchString).subscribe(res => {
      if (page === 0) {
        this.listTemplateType = res.content;
      } else {
        this.listTemplateType = this.listTemplateType.concat(res.content);
      }
      this.totalPagesTemplateType = res.totalPages;
    }, err => {
      console.log(err);
    });
  }

}
