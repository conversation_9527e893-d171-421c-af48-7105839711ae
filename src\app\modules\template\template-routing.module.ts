import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from 'src/app/core/guard/auth.guard';
import { AdminLayoutComponent } from 'src/app/layouts/admin/admin-layout/admin-layout.component';
import { AdminLayoutModule } from 'src/app/layouts/admin/admin-layout.module';
import { ListTemplateComponent } from './list-template/list-template.component';

const routes: Routes = [
  {
    path: '',
    component: AdminLayoutComponent,
    children: [
      { path: '', component: ListTemplateComponent },
    ],
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['reporterAdminMaster', 'reporterTemplateCategory'],
      permissions: ['reporterAdminMaster']
    }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes), AdminLayoutModule],
  exports: [RouterModule]
})
export class TemplateRoutingModule { }
