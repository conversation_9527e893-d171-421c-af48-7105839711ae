import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormService } from 'src/app/data/service/form/form.service';

@Component({
  selector: 'app-delete-form',
  templateUrl: './delete-form.component.html',
  styleUrls: ['./delete-form.component.scss']
})
export class DeleteFormComponent implements OnInit {
  formId: string;
  formName: string;

  constructor(
    public dialogRef: MatDialogRef<DeleteFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDeleteDialogModel,
    private formService: FormService
  ) {
    this.formId = data.id;
    this.formName = data.name;
  }

  ngOnInit(): void {
  }

  onConfirm() {
    this.formService.deleteForm(this.formId).subscribe(data => {
      this.dialogRef.close(true);
    }, err => {
      this.dialogRef.close(false);
    });
  }

  onDismiss() {
    this.dialogRef.close();
  }

}

export class ConfirmDeleteDialogModel {
  constructor(public id: string, public name: string) {
  }
}
