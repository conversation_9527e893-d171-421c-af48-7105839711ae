{"name": "web-reporter", "version": "0.0.0", "scripts": {"ng": "ng", "xi18n": "ng xi18n", "start": "ng serve", "build": "ng build", "build-i18n": "ng build --configuration=production-vi && ng build --configuration=production-en", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular-material-components/datetime-picker": "^2.0.4", "@angular-material-components/moment-adapter": "^2.0.2", "@angular/animations": "~9.1.3", "@angular/cdk": "^9.2.4", "@angular/common": "~9.1.3", "@angular/compiler": "~9.1.3", "@angular/core": "~9.1.3", "@angular/flex-layout": "^9.0.0-beta.31", "@angular/forms": "~9.1.3", "@angular/localize": "^9.1.4", "@angular/material": "^9.2.4", "@angular/platform-browser": "~9.1.3", "@angular/platform-browser-dynamic": "~9.1.3", "@angular/router": "~9.1.3", "@ckeditor/ckeditor5-angular": "^1.2.3", "@ckeditor/ckeditor5-build-classic": "^21.0.0", "@ngx-loading-bar/core": "^5.1.0", "@ngx-loading-bar/http-client": "^5.1.0", "@ngx-loading-bar/router": "^5.1.0", "angular-material": "^1.1.24", "axios": "^0.20.0", "bpmn-js": "^7.3.0", "exceljs": "^4.1.1", "file-saver": "^2.0.2", "font-awesome": "^4.7.0", "jwt-decode": "^2.2.0", "keycloak-angular": "^7.2.0", "keycloak-js": "^9.0.3", "ng-mat-select-infinite-scroll": "^2.1.1", "ngx-filesaver": "^10.0.1", "ngx-gallery-9": "^1.0.6", "ngx-image-compress": "^8.0.4", "ngx-mat-select-search": "^3.1.0", "ngx-owl-carousel-o": "^3.0.1", "ngx-pagination": "^5.0.0", "ngx-print": "^1.2.0-beta.5", "ngx-ui-loader": "^9.1.1", "jsoneditor": "^9.1.3", "ang-jsoneditor": "^1.10.3", "rxjs": "~6.5.4", "tslib": "^1.10.0", "xlsx": "^0.16.7", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "~0.901.3", "@angular/cli": "~9.1.3", "@angular/compiler-cli": "~9.1.3", "@angular/language-service": "~9.1.3", "@types/jasmine": "^3.5.13", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.12.47", "codelyzer": "^5.1.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~5.0.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~2.1.0", "karma-jasmine": "~3.0.1", "karma-jasmine-html-reporter": "^1.4.2", "protractor": "^7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~3.8.3"}}