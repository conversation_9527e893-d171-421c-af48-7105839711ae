export const environment = {
  production: true,
  loadConfigFromUrl: true,
  configUrl: './assets/app.config.json',
  routerConfig: {
    enableTracing: false
  },
  config: {
    keycloakDebug: true,
    keycloakOptions: {
      config: {
        url: 'https://digo-oidc.vnptigate.vn/auth',
        realm: 'digo',
        clientId: 'web-reporter'
      },
      initOptions: {
        onLoad: 'check-sso'
      },
      bearerExcludedUrls: ['/sy/app-deployment']
    },
    insufficientPermissionRouterLink: 'error/insufficient-permission',
    apiProviders: {
      digo: {
        rootUrl: 'https://digo-api.vnptigate.vn',
        services: {
          basedata: { path: 'ba' },
          basecat: { path: 'bt' },
          fileman: { path: 'fi' },
          human: { path: 'hu' },
          postman: { path: 'po' },
          logman: { path: 'lo' },
          surfeed: { path: 'su' },
          messenger: { path: 'me' },
          padman: { path: 'pa' },
          basepad: { path: 'bd' },
          reporter: { path: 're' },
          bpm: { path: 'bpm' },
          modeling: { path: 'modeling-service' },
          sysman: { path: 'sy' }
        }
      }
    },
    deploymentId: '5dd2a75c2078ed3388ab043d',
    deploymentUrl: 'https://apicloud.vncitizens.vn/sy',
    appCode: 'web-reporter',
    provinceDefaultId: 82,
    languageDefaultId: 228,
    languageDefault: 'vi',
    reloadTimeout: 2000,
    expiredTime: 5000,
    translatePaginator: ['Số dòng', 'Trang đầu', 'Trang trước', 'Trang tiếp theo', 'Trang cuối', 'của'],
    pageSizeOptions: [5, 10, 20, 50],
    searchAfterStopTyping: 800,
    fileUnits: ['bytes', 'KB', 'MB', 'GB', 'TB', 'PB'],
    translateNotificationLabel: {
      vi: {
        success: {
          post: 'Thêm mới thành công!',
          put: 'Cập nhật thành công!',
          delete: 'Xoá thành công!',
        },
        error: {
          post: 'Thêm mới thất bại!',
          put: 'Cập nhật thất bại!',
          delete: 'Xoá thất bại!',
        }
      },
      en: {
        success: {
          post: 'Successfully added!',
          put: 'Successfully updated !',
          delete: 'Successfully deleted!',
        },
        error: {
          post: 'Add failed!',
          put: 'Update failed!',
          delete: 'Delete failed!',
        }
      }
    },
    regValidators: '/[-a-zA-Z0-9@:%_\+.~#?&//=]{2,256}\.[a-z]{2,4}\b(\/[-a-zA-Z0-9@:%_\+.~#?&//=]*)?/gi',
    // Date picker format
    PICK_FORMATS: {
      parse: {
        dateInput: {
          month: 'short',
          year: 'numeric',
          day: 'numeric'
        }
      },
      display: {
        dateInput: 'input',
        monthYearLabel: {
          year: 'numeric',
          month: 'short'
        },
        dateA11yLabel: {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        },
        monthYearA11yLabel: {
          year: 'numeric',
          month: 'long'
        }
      }
    },
    placeParentTypeProvinceId: '5ee304423167922ac55bea01',
    placeParentTypeDistrictId: '5ee304423167922ac55bea02',
    placeParentTypeWardId: '5ee304423167922ac55bea03',
    placeDefaultProvinceId: '5def47c5f47614018c000082',
    dossierTaskNameCategoryId: '5f3a491c4e1bd312a6f00013',
    agencyLevelCategoryId: '5f3a491c4e1bd312a6f00009',
    procedureLevelCategoryId: '5f3a491c4e1bd312a6f00008',
    applyMethodCategoryId: '5f3a491c4e1bd312a6f00010',
    monetaryUnitCategoryId: '5f5b259f4e1bd312a6f3ae1c',
    caseCategoryId: '5f3f3a944e1bd312a6f3adde',
    documentTypeCategoryId: '5f3a491c4e1bd312a6f00002',
    paymentMethodCategoryId: '5f3a491c4e1bd312a6f00011',
    formGroupCategoryId: '5f5b27044e1bd312a6f3ae1d',
    ticketTypeCategoryId: '5f5b27924e1bd312a6f3ae1e',
    implementerCategoryId: '5f5b27fd4e1bd312a6f3ae1f',
    directPaymentMethodId: '5f7fca83b80e603d5300dcf4',
    onlinePaymentMethodId: '5f7fca9fb80e603d5300dcf5',
    postofficePaymentMethodId: '5f7fcaadb80e603d5300dcf6',

    procedureDetailURL: 'https://digo-padsvc.vnptigate.vn/vi/procedure/detail/',
    defaultUserAvatar: 'https://staticcloud.vncitizens.vn/logo/HCC.png',
    defaultLogo: 'https://staticcloud.vncitizens.vnn/logo/quoc-huy.png',
    cloudStaticURL: 'https://staticcloud.vncitizens.vn/',
    webAccountURL: 'https://accountdev.vncitizens.vn/',
    uiLoaderConfig: {
      bgsColor: '#000000',
      bgsOpacity: 0.5,
      bgsPosition: 'bottom-right',
      bgsSize: 60,
      bgsType: 'ball-spin-clockwise',
      blur: 0,
      delay: 0,
      fastFadeOut: true,
      fgsColor: '#eb6a35',
      fgsPosition: 'center-center',
      fgsSize: 120,
      fgsType: 'ball-scale-multiple',
      gap: 24,
      masterLoaderId: 'master',
      overlayBorderRadius: '0',
      overlayColor: 'rgb(255,255,255)',
      pbColor: 'red',
      pbDirection: 'ltr',
      pbThickness: 3,
      hasProgressBar: false,
      text: '',
      textColor: '#FFFFFF',
      textPosition: 'center-center',
      maxTime: -1,
      minTime: 300
    },
    acceptFileExtension: ['.DOC', '.DOCX', '.PDF', '.XLS', '.XLSX', '.TXT', '.JPG', '.JPEG', '.PNG'],
    acceptFileType: [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/pdf',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png'
    ],
    acceptFileExtensionTemplate: ['.RPTDESIGN', '.FTL'],
    listSubsystem: [
      {
        id: '5f7c16069abb62f511880001',
        code: 'svc-sysman',
        name: [
          {
            languageId: 228,
            name: 'Dữ liệu dùng chung và xác thực'
          },
          {
            languageId: 46,
            name: 'Dữ liệu dùng chung và xác thực'
          }
        ]
      },
      {
        id: '5f7c16069abb62f511880002',
        code: 'svc-petiton',
        name: [
          {
            languageId: 228,
            name: 'Phản ánh kiến nghị và 1022'
          },
          {
            languageId: 46,
            name: 'Phản ánh kiến nghị và 1022'
          }
        ]
      },
      {
        id: '5f7c16069abb62f511880003',
        code: 'svc-docman',
        name: [
          {
            languageId: 228,
            name: 'Office 2020'
          },
          {
            languageId: 46,
            name: 'Office 2020'
          }
        ]
      },
      {
        id: '5f7c16069abb62f511880004',
        code: 'svc-isoman',
        name: [
          {
            languageId: 228,
            name: 'VNPT ISO'
          },
          {
            languageId: 46,
            name: 'VNPT ISO'
          }
        ]
      },
      {
        id: '5f7c16069abb62f511880005',
        code: 'web-onegate',
        name: [
          {
            languageId: 228,
            name: 'Web một cửa'
          },
          {
            languageId: 46,
            name: 'Web một cửa'
          }
        ]
      },
      {
        id: '5f7c16069abb62f511880006',
        code: 'web-padsvc',
        name: [
          {
            languageId: 228,
            name: 'Web dịch vụ công'
          },
          {
            languageId: 46,
            name: 'Web dịch vụ công'
          }
        ]
      },
      {
         "id": "5f7c16069abb62f511880007",
         "code": "web-kiosk",
         "name": [
            {
               "languageId": 228,
               "name": "Web Kiosk"
            },
            {
               "languageId": 46,
               "name": "Web Kiosk"
            }
         ]
      }
    ],
    siteName: {
      vi: 'Tỉnh Tiền Giang',
      en: 'Tien Giang Province'
    },
    siteTitle: {
      vi: 'Quản trị báo cáo, phiếu động',
      en: 'Report management, dynamic report'
    }
  }
};
