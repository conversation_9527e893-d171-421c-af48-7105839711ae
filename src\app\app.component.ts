import { Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { EnvService } from 'src/app/core/service/env.service';
import { Title } from '@angular/platform-browser';
import { MainService } from './data/service/main/main.service';
import { DeploymentService } from './data/service/deployment.service';
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  config = this.envService.getConfig();

  constructor(
    private envService: EnvService,
    @Inject(LOCALE_ID) protected localeId: string,
    private titleService: Title,
    private deploymentService: DeploymentService,
    @Inject(DOCUMENT) private doc: any,
  ) {
    this.deploymentService.setAppDeployment();
  }

  async ngOnInit(): Promise<void> {
    localStorage.setItem('language', this.localeId);
    if (this.localeId === 'vi') {
      localStorage.setItem('languageId', '228');
      this.titleService.setTitle(this.config.siteTitle.vi);
    }
    if (this.localeId === 'en') {
      localStorage.setItem('languageId', '46');
      this.titleService.setTitle(this.config.siteTitle.en);
    }

    const siteNameArr = [{
      vi: this.config.siteName.vi,
      en: this.config.siteName.en
    }];
    if (localStorage.getItem('siteName') === '' || localStorage.getItem('siteName') === null ) {
      localStorage.setItem('siteName', JSON.stringify(siteNameArr, null, 2));
    }
    if (localStorage.getItem('selectedAgencyId') === null ) {
      localStorage.setItem('selectedAgencyId', '');
      localStorage.setItem('selectedAgencyName', '');
    }
    if (this.deploymentService.getAppDeployment()?.env?.enableSmartUX == 1) {
      this.integrateSmartUX();
    }
  }
  
  integrateSmartUX(){
    console.log(this.deploymentService.getAppDeployment()?.SmartUXScript);
    let decodedScript = this.htmlDecode(this.deploymentService.getAppDeployment()?.SmartUXScript);
        decodedScript = decodedScript.replace(/<script>/g, '').replace(new RegExp('</script>', 'g'), '');
        const script = this.doc.createElement('script');
        script.type = 'text/javascript';
        script.innerHTML = decodedScript;
        const head = this.doc.getElementsByTagName('head')[0];
        head.appendChild(script);
  }
  htmlDecode(input) {
    const doc = new DOMParser().parseFromString(input, 'text/html');
    return doc.documentElement.textContent.toString();
  }
}
