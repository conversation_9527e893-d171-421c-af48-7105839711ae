<h2 i18n><PERSON><PERSON><PERSON><PERSON> tr<PERSON> phiếu động</h2>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_searchbar" fxFlex="grow">
        <form [formGroup]="searchForm" (submit)="onConfirm()" class="searchForm">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="space-between" >
                <mat-form-field appearance="outline" fxFlex.gt-sm="25" fxFlex.gt-xs="25" fxFlex='grow'>
                    <mat-label i18n>Nhập từ khoá</mat-label>
                    <input matInput formControlName="keyword">
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-sm="25" fxFlex.gt-xs="25" fxFlex='grow'>
                    <mat-label i18n><PERSON><PERSON> hệ</mat-label>
                        <mat-select value="" formControlName="subsystem">
                        <mat-option value="" i18n>Tất cả</mat-option>
                        <mat-option *ngFor='let subsystem of listSubsystem' value="{{subsystem.id}}">
                            {{ subsystem.name }}</mat-option>
                    </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-sm="25" fxFlex.gt-xs="25" fxFlex='grow'>
                    <mat-label i18n>Loại phiếu động</mat-label>
                    <mat-select msInfiniteScroll (infiniteScroll)="getNextBatch('templateType')"
                        [complete]="totalPagesTemplateType <= currentPageTemplateType+1"
                        formControlName="templateType">
                        <!-- Tạm thời tắt tính năng search trong combobox -->
                        <!-- <div>
                            <div>
                                <input matInput #searchInputTemplateType (keyup)="onEnter('templateType', $event)"
                                    (keydown)="$event.stopPropagation()" placeholder="Nhập từ khóa"
                                    class="search-nested" />
                                <button mat-icon-button class="clear-search-nested"
                                    *ngIf="searchInputTemplateType.value !== ''"
                                    (click)="searchInputTemplateType.value = ''; resetSearchForm('templateType')">
                                    <mat-icon> close </mat-icon>
                                </button>
                            </div>
                        </div> -->
                        <mat-option value="" i18n>Tất cả</mat-option>
                        <mat-option *ngFor="let item of listTemplateType" value="{{ item.id }}"> {{ item.name }}</mat-option>
                    </mat-select>
                </mat-form-field>
                <div fxFlex='1'></div>
                <button mat-flat-button fxFlex.gt-sm="17" fxFlex='20' class="searchBtn" type="submit">
                    <mat-icon>search</mat-icon> <span i18n>Tìm kiếm</span>
                </button>
            </div>
        </form>
    </div>
</div>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <button mat-flat-button class="btn_add" (click)="addTemplate()">
            <mat-icon>add</mat-icon>
            <span i18n>Thêm mới</span>
        </button>
        <div class="frm_tbl">
            <table mat-table
                [dataSource]="dataSource">
                <ng-container matColumnDef="stt">
                    <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="STT" i18n-data-label> {{row.stt}} </mat-cell>
                </ng-container>

                <ng-container matColumnDef="name">
                    <mat-header-cell *matHeaderCellDef i18n>Tên phiếu động</mat-header-cell>
                    <mat-cell *matCellDef="let row" #tooltip="matTooltip" matTooltip="{{row.name}}"
                        matTooltipPosition="right" data-label="Tên phiếu động" i18n-data-label class="cell_code">
                        <span>{{row.name}}</span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="subsystem">
                    <mat-header-cell *matHeaderCellDef i18n>Phân hệ</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Phân hệ" i18n-data-label> {{row.subsystem}}
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="typeName">
                    <mat-header-cell *matHeaderCellDef i18n>Loại phiếu động</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Loại phiếu động" i18n-data-label> {{row.typeName}}
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="fileName">
                    <mat-header-cell *matHeaderCellDef i18n>File phiếu động</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="File phiếu động" i18n-data-label class="fileNameCell"> {{row.file.filename}}
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="action">
                    <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Thao tác" i18n-data-label>
                        <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                            <mat-icon>more_horiz</mat-icon>
                        </button>
                        <mat-menu #actionMenu="matMenu" xPosition="before">
                            <button mat-menu-item class="menuAction" (click)="downloadFile(row.id, row.file.filename)">
                                <mat-icon>cloud_download</mat-icon> <span i18n>Tải xuống</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="updateTemplateDialog(row.id)">
                                <mat-icon>edit</mat-icon> <span i18n>Cập nhật</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="deleteTemplateDialog(row.id, row.name, row.file.path)">
                                <mat-icon>delete_outline</mat-icon> <span i18n>Xóa</span>
                            </button>
                        </mat-menu>
                    </mat-cell>
                </ng-container>

                

                <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </table>
            <div class="frm_Pagination">
                <ul class="temp_Arr">
                    <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}"></li>
                </ul>
                <div class="pageSize">
                    <span i18n>Hiển thị </span>
                    <mat-form-field appearance="outline">
                        <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                            <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <span i18n>trên {{countResult}} bản ghi</span>
                </div>
                <div class="control">
                    <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true"
                        previousLabel="" nextLabel="">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
</div>