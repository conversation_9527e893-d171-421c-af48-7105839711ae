import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { KeycloakAuthGuard, KeycloakService } from 'keycloak-angular';
import { EnvService } from '../service/env.service';
import { PermissionClaim } from 'src/app/data/schema/oidc/token/permission-claim';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard extends KeycloakAuthGuard {

  constructor(protected router: Router, protected keycloakAngular: KeycloakService, private envService: EnvService) {
    super(router, keycloakAngular);
  }

  isAccessAllowed(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Promise<boolean> {
    let config = this.envService.getConfig();
    return new Promise((resolve, reject) => {

      if (!this.authenticated) {
        this.keycloakAngular.login().catch(e => console.error(e));
        return reject(false);
      }

      //check required roles
      const roles: string[] = route.data.roles;
      if (roles && roles.length > 0) {
        if (!this.roles || this.roles.length === 0) {
          this.router.navigate([config.insufficientPermissionRouterLink]);
          return resolve(false);
        }
        if (roles.some(role => this.roles.indexOf(role) === -1)) {
          this.router.navigate([config.insufficientPermissionRouterLink]);
          return resolve(false);
        }
      }

      //check required any roles
      const anyRoles: string[] = route.data.anyRoles;
      if (anyRoles && anyRoles.length > 0) {
        if (!this.roles || this.roles.length === 0) {
          this.router.navigate([config.insufficientPermissionRouterLink]);
          return resolve(false);
        }
        if (anyRoles.every(role => this.roles.indexOf(role) === -1)) {
          this.router.navigate([config.insufficientPermissionRouterLink]);
          return resolve(false);
        }
      }

      let grantedPermissions: PermissionClaim[] = [];
      let token = <any> this.keycloakAngular.getKeycloakInstance().tokenParsed;
      if (token && token.permissions && token.permissions instanceof Array) {
        grantedPermissions = token.permissions;
      }
      
      //check required permissions
      const permissions: string[] = route.data.permissions;
      if (permissions && permissions.length > 0) {
        if (grantedPermissions.length === 0) {
          this.router.navigate([config.insufficientPermissionRouterLink]);
          return resolve(false);
        }
        if (permissions.some(p => grantedPermissions.every(gp => gp.permission.code !== p))) {
          this.router.navigate([config.insufficientPermissionRouterLink]);
          return resolve(false);
        }
      }
      
      //check required any permissions
      const anyPermissions: string[] = route.data.anyPermissions;
      if (anyPermissions && anyPermissions.length > 0) {
        if (grantedPermissions.length === 0) {
          this.router.navigate([config.insufficientPermissionRouterLink]);
          return resolve(false);
        }
        if (anyPermissions.every(p => grantedPermissions.every(gp => gp.permission.code !== p))) {
          this.router.navigate([config.insufficientPermissionRouterLink]);
          return resolve(false);
        }
      }

      return resolve(true);
    });
  }

}
