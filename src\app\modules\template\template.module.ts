import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TemplateRoutingModule } from './template-routing.module';
import { ListTemplateComponent } from './list-template/list-template.component';
import {MatExpansionModule} from '@angular/material/expansion';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import { AdminLayoutModule } from 'src/app/layouts/admin/admin-layout.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { AddTemplateComponent } from './dialogs/add-template/add-template.component';
import { DeleteTemplateComponent } from './dialogs/delete-template/delete-template.component';
import { UpdateTemplateComponent } from './dialogs/update-template/update-template.component';

@NgModule({
  declarations: [ListTemplateComponent, AddTemplateComponent, DeleteTemplateComponent, UpdateTemplateComponent],
  imports: [
    CommonModule,
    TemplateRoutingModule,
    AdminLayoutModule,
    MatExpansionModule,
    SharedModule,
    MatButtonModule,
    MatIconModule,
  ]
})
export class TemplateModule { }
