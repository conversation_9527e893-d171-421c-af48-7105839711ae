import {
  AfterContentInit,
  Component,
  ElementRef,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  ViewChild,
  SimpleChanges,
  EventEmitter,
  Renderer2,

} from '@angular/core';

import { HttpClient, HttpHeaders } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import * as BpmnJS from 'bpmn-js/dist/bpmn-navigated-viewer.production.min.js';
import { importDiagram } from 'src/app/data/schema/rx';
import { throwError } from 'rxjs';
import { KeycloakService } from 'keycloak-angular';
import axios from "axios";


@Component({
  selector: 'app-process-diagram',
  templateUrl: './process-diagram.component.html',
  styleUrls: ['./process-diagram.component.scss']
})
export class ProcessDiagramComponent implements AfterContentInit, OnChanges, OnDestroy {

  private bpmnJS: BpmnJS;
  private canvas;

  @ViewChild('ref', { static: true }) private el: ElementRef;
  @Output() private importDone: EventEmitter<any> = new EventEmitter();
  @Output() private elementClick: EventEmitter<any> = new EventEmitter();
  @Output() private taskElements: EventEmitter<any> = new EventEmitter();
  @Input() private url: string;


  constructor(
    private http: HttpClient,
    private keycloak: KeycloakService,
    private renderer: Renderer2
  ) {
    this.bpmnJS = new BpmnJS();
    this.bpmnJS.on('import.done', ({ error }) => {
      if (!error) {
        this.bpmnJS.get('canvas').zoom('fit-viewport');
      }
    });
  }

  ngAfterContentInit(): void {
    this.bpmnJS.attachTo(this.el.nativeElement);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.url) {
      this.loadUrl(changes.url.currentValue);
    }
  }

  ngOnDestroy(): void {
    this.bpmnJS.destroy();
  }

  loadUrl(url: string) {
    const token = this.keycloak.getKeycloakInstance().token;
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    return (
      this.http.get(url, { headers, responseType: 'text' }).pipe(catchError(err =>
        throwError(err)), importDiagram(this.bpmnJS)).subscribe((warnings) => {
          this.importDone.emit({
            type: 'success',
            warnings
          });
        }, (err) => {
          this.importDone.emit({
            type: 'error',
            error: err
          });
        }
        )
    );
  }



  nodeClick(node) {
    console.log(node);
    let overlays = this.bpmnJS.get('overlays');
    let elementRegistry = this.bpmnJS.get('elementRegistry');
    let shape = elementRegistry.get(node.id);
    console.log(elementRegistry);
    console.log(shape);
    alert(shape.width + '=' + shape.height)
    console.log("overlays");
    console.log(overlays);
    overlays.add(node.id, {
      position: {
        top: 0,
        left: 0
      },
      html: '<div class="highlight-overlay" style="width:' + shape.width + 'px;height:' + shape.height + 'px">'
    });

    this.elementClick.emit({
      node: node
    });

  }

  public highlight(value) {
    alert(value);
  }

  ngAfterViewInit() {
    this.canvas = this.bpmnJS.get('canvas');
    this.bpmnJS.on('element.click', (event) => this.nodeClick(event.element));
    console.log('=====================================================');
    let elementRegistry = this.bpmnJS.get('elementRegistry')

    console.log(elementRegistry)

    const tasks = [];
    console.log(elementRegistry['_elements'])
    console.log(elementRegistry['_elements'].Task_0xzis4q)

    let abc = elementRegistry['_elements'];

    console.log('=====================================================');

    elementRegistry.forEach(key => {
      console.log(key);
    });

    this.taskElements.emit({
      tasks: tasks
    });
    
  }

  zoomIn() {
    this.bpmnJS.get('zoomScroll').stepZoom(1);
  }

  zoomOut() {
    this.bpmnJS.get('zoomScroll').stepZoom(-1);
  }

  resetZoom() {
    this.bpmnJS.get('zoomScroll').reset();
  }

  downloadSVG() {
    this.bpmnJS.saveSVG({ format: true }, (error, svg) => {
      if (error) {
        return;
      }

      const svgBlob = new Blob([svg], {
        type: 'image/svg+xml'
      });

      const fileName = Math.random().toString().substring(7) + '.svg';

      const downloadLink = document.createElement('a');
      downloadLink.download = fileName;
      downloadLink.innerHTML = 'Get BPMN SVG';
      downloadLink.href = window.URL.createObjectURL(svgBlob);
      downloadLink.onclick = (event) => {
        document.body.removeChild(event.target as Node);
      };
      downloadLink.style.visibility = 'hidden';
      document.body.appendChild(downloadLink);
      downloadLink.click();
    });
  }
}
