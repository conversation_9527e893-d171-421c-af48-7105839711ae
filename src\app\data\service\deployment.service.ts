import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class DeploymentService {

  renewDeploymentConfig = new Subject<boolean>();

  constructor(
    private http: HttpClient,
    private envService: EnvService,
  ) { }

  private getAppDeploymentUrls = this.envService.getConfig().deploymentUrl +
    `/app-deployment?deployment-id=${this.envService.getConfig().deploymentId}&app-code=${this.envService.getConfig().appCode}`;

  setAppDeployment(): any {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    this.http.get(this.getAppDeploymentUrls, { headers, responseType: 'json' }).subscribe((res: any) => {
      localStorage.setItem('deploymentVariables', JSON.stringify(res));
      return res.configuration;
      // setTimeout(() => {
      //   this.renewDeploymentConfig.next(true);
      // }, this.envService.getConfig().timeOut);
    });
  }

  getAppDeployment(): any {
    const deploymentVariables = JSON.parse(localStorage.getItem('deploymentVariables'));
    if (deploymentVariables) {
      return deploymentVariables.configuration;
    } else {
      return this.setAppDeployment();
      // setTimeout(() => {
      //   return this.getAppDeployment();
      // }, this.envService.getConfig().timeOut);
    }
  }

  getMapsConfig(): any {
    const config = this.getAppDeployment();
    return config.maps;
  }
}
