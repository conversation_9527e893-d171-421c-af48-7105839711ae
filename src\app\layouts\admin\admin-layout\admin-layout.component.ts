import { Component, OnInit } from '@angular/core';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { Router } from '@angular/router';

@Component({
  selector: 'app-admin-layout',
  templateUrl: './admin-layout.component.html',
  styleUrls: ['./admin-layout.component.scss']
})
export class AdminLayoutComponent implements OnInit {

  constructor(
    private router: Router,
    private ngxService: NgxUiLoaderService
  ) { }

  ngOnInit(): void {
    const path = this.router.url;
    const curRoute = path.substring(0, path.lastIndexOf('/'));
    if (curRoute === '/procedure/update') {
      this.ngxService.startLoader('initLoader');
      setTimeout(() => {
        this.ngxService.stopLoader('initLoader');
      }, 1000);
    }
  }

}
