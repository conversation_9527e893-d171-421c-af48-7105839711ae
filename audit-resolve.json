{"decisions": {"1500|protractor>yargs>yargs-parser": {"decision": "fix", "madeAt": 1603157809648}, "1523|@angular/localize>@babel/core>@babel/generator>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/core>@babel/generator>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/generator>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/generator>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/generator>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular/localize>@babel/core>@babel/template>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular/localize>@babel/core>@babel/traverse>@babel/helper-function-name>@babel/template>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/core>@babel/traverse>@babel/helper-function-name>@babel/template>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/traverse>@babel/helper-function-name>@babel/template>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/traverse>@babel/helper-function-name>@babel/template>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/core>@babel/template>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/template>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/template>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/template>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular/localize>@babel/core>@babel/traverse>@babel/generator>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/core>@babel/traverse>@babel/generator>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/traverse>@babel/generator>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/traverse>@babel/generator>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular/localize>@babel/core>@babel/traverse>@babel/helper-function-name>@babel/helper-get-function-arity>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/core>@babel/traverse>@babel/helper-function-name>@babel/helper-get-function-arity>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/traverse>@babel/helper-function-name>@babel/helper-get-function-arity>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/traverse>@babel/helper-function-name>@babel/helper-get-function-arity>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular/localize>@babel/core>@babel/traverse>@babel/helper-function-name>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/core>@babel/traverse>@babel/helper-function-name>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/traverse>@babel/helper-function-name>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/traverse>@babel/helper-function-name>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular/localize>@babel/core>@babel/traverse>@babel/helper-split-export-declaration>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/core>@babel/traverse>@babel/helper-split-export-declaration>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/traverse>@babel/helper-split-export-declaration>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/traverse>@babel/helper-split-export-declaration>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular/localize>@babel/core>@babel/traverse>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/core>@babel/traverse>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/traverse>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/traverse>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular/localize>@babel/core>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/core>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/preset-env>@babel/preset-modules>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197696}, "1523|@angular-devkit/build-angular>@babel/preset-env>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/types>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular/localize>@babel/core>@babel/generator>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>@babel/core>@babel/generator>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/generator>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>@babel/generator>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/generator>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular/localize>@babel/core>@babel/traverse>@babel/generator>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>@babel/core>@babel/traverse>@babel/generator>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/traverse>@babel/generator>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/traverse>@babel/generator>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular/localize>@babel/core>@babel/traverse>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>@babel/core>@babel/traverse>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>@babel/traverse>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|karma-coverage-istanbul-reporter>istanbul-api>istanbul-lib-instrument>@babel/traverse>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular/localize>@babel/core>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>@babel/core>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>@jsdevtools/coverage-istanbul-loader>istanbul-lib-instrument>@babel/core>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>source-map-loader>async>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>webpack-dev-server>portfinder>async>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|karma-coverage-istanbul-reporter>istanbul-api>async>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>webpack-dev-server>http-proxy-middleware>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular-devkit/build-angular>webpack-merge>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|@angular/cli>inquirer>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1523|karma>lodash": {"decision": "fix", "madeAt": 1603158197697}, "1561|@angular-devkit/build-angular>webpack-dev-server>selfsigned>node-forge": {"decision": "fix", "madeAt": 1603158259979}}, "rules": {}, "version": 1}