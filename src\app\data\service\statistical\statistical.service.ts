import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class StatisticalService {
  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private getReporterURL = this.apiProviderService.getUrl('digo', 'reporter');
  private getAgencyURL = this.apiProviderService.getUrl('digo', 'basedata');

  getReportForDossier6a(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-6a' + searchString, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/dossier/--by-6a' + searchString, { headers }).pipe();
  }

  getReportForDossier6b(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-6b' + searchString, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/dossier/--by-6b' + searchString, { headers }).pipe();
  }

  getReportForDossier6d(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-6d' + searchString, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/dossier/--by-6d' + searchString, { headers }).pipe();
  }

  getProcedureReport(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0 ' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-procedure?' + searchString, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/dossier/--by-procedure' + searchString, { headers }).pipe();
  }

  getSectorReport(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0 ' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-unresolved-overdue?' + searchString, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/dossier/--by--unresolved-overdue' + searchString, { headers }).pipe();
  }

  getListAgency(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let kq = '';
    if (id !== null && id !== ''){
      kq = '&parent-id=' + id;
    }
    return this.http.get(this.getAgencyURL + '/agency/' + '?page=0&size=50' + kq, { headers }).pipe();
  }
}
