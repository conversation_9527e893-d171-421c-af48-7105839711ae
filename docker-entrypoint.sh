#!/bin/sh

echo "----------------"
echo "ENTRYPOINT START"
echo "----------------"
echo ""

digo_supported_lang="vi en"
for lang in $digo_supported_lang; do

  index_file="./${lang}/index.html"
  index_bak_file="./${lang}/index.html.bak"
  config_file="./${lang}/assets/app.config.json"
  config_bak_file="./${lang}/assets/app.config.json.bak"
  exists_bak_file=1

  if [ ! -f $config_bak_file ]; then
    echo "> copy $config_file to $config_bak_file";
    cp $config_file $config_bak_file
    echo "> copy $index_file to $index_bak_file";
    cp $index_file $index_bak_file
    exists_bak_file=0
  fi

  if [ $exists_bak_file -eq 1 ]; then
    echo "> copy $config_bak_file to $config_file";
    cp $config_bak_file $config_file;
    echo "> copy $index_bak_file to $index_file";
    cp $index_bak_file $index_file;
  fi

  if [ -n "${KEYCLOAK_URL}" ]; then
    replace='\/'
    encodedValue=${KEYCLOAK_URL//\//$replace}
    sed -e "s/\"url\": \"\${KEYCLOAK_URL}\"/\"url\": \"$encodedValue\"/g" -i $config_file
    sed -e "s,\${KEYCLOAK_URL}\/,${KEYCLOAK_URL}\/,g" -i $config_file
  fi

  if [ -n "${KEYCLOAK_REALM}" ]; then
    sed -e "s,\${KEYCLOAK_REALM},${KEYCLOAK_REALM},g" -i $config_file
  fi

  if [ -n "${OAUTH2_CLIENT_ID}" ]; then
    sed -e "s,\${OAUTH2_CLIENT_ID},${OAUTH2_CLIENT_ID},g" -i $config_file
  fi

  if [ -n "${DIGO_API_GATEWAY}" ]; then
    sed -e "s,\${DIGO_API_GATEWAY},${DIGO_API_GATEWAY},g" -i $config_file
  fi
  
  if [ -n "${STATIC_URL}" ]; then
	sed -e "s,\${STATIC_URL},${STATIC_URL},g" -i $config_file
  fi  
  
  if [ -n "${VI_SITENAME}" ]; then
	sed -e "s,\${VI_SITENAME},${VI_SITENAME},g" -i $config_file
  fi  
  
  if [ -n "${EN_SITENAME}" ]; then
	sed -e "s,\${EN_SITENAME},${EN_SITENAME},g" -i $config_file
  fi  
  
  if [ -n "${VI_SITETITLE}" ]; then
	sed -e "s,\${VI_SITETITLE},${VI_SITETITLE},g" -i $config_file
  fi  
  
  if [ -n "${EN_SITETITLE}" ]; then
	sed -e "s,\${EN_SITETITLE},${EN_SITETITLE},g" -i $config_file
  fi 

  if [ -n "${APP_CODE}" ]; then
    sed -e "s,\${APP_CODE},${APP_CODE},g" -i $config_file
  fi

  if [ -n "${DEPLOYMENT_ID}" ]; then
    sed -e "s,\${DEPLOYMENT_ID},${DEPLOYMENT_ID},g" -i $config_file
  fi

  if [ -n "${DIGO_DEPLOYMENT_URL}" ]; then
    sed -e "s,\${DIGO_DEPLOYMENT_URL},${DIGO_DEPLOYMENT_URL},g" -i $config_file
  fi

done

nginx -g "daemon off;"
