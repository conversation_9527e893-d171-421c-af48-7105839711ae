import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MaterialModule } from './material.module';
import { SilentCheckSsoComponent } from './components/silent-check-sso/silent-check-sso.component';
import { SpinnerComponent } from './components/spinner/spinner.component';
import { SnackbarComponent } from './components/snackbar/snackbar.component';
import { ProcessDiagramComponent } from './components/process-diagram/process-diagram.component';
import { HttpClientModule } from '@angular/common/http';
import { FlexLayoutModule } from '@angular/flex-layout';
import { NgxPaginationModule } from 'ngx-pagination';
import { LoadingBarRouterModule } from '@ngx-loading-bar/router';
import { LoadingBarHttpClientModule } from '@ngx-loading-bar/http-client';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { NgxMatDatetimePickerModule, NgxMatTimepickerModule } from '@angular-material-components/datetime-picker';
import { NgxUiLoaderModule } from 'ngx-ui-loader';
import { environment } from 'src/environments/environment';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatSelectInfiniteScrollModule } from 'ng-mat-select-infinite-scroll';
import { NgJsonEditorModule } from 'ang-jsoneditor';

const ngxUiLoaderConfig: any = environment.config.uiLoaderConfig;

@NgModule({
  declarations: [SilentCheckSsoComponent, SpinnerComponent, SnackbarComponent, ProcessDiagramComponent],
  imports: [
    CommonModule,
    MaterialModule,
    HttpClientModule,
    LoadingBarRouterModule,
    LoadingBarHttpClientModule,
    NgxPaginationModule,
    FlexLayoutModule,
    CKEditorModule,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule,
    NgxUiLoaderModule.forRoot(ngxUiLoaderConfig),
    NgxMatSelectSearchModule,
    MatSelectInfiniteScrollModule,
    NgJsonEditorModule
  ],
  exports: [
    CommonModule,
    MaterialModule,
    HttpClientModule,
    LoadingBarRouterModule,
    LoadingBarHttpClientModule,
    NgxPaginationModule,
    FlexLayoutModule,
    CKEditorModule,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule,
    NgxUiLoaderModule,
    NgxMatSelectSearchModule,
    MatSelectInfiniteScrollModule,
    NgJsonEditorModule,
    ProcessDiagramComponent
  ]
})
export class SharedModule { }
