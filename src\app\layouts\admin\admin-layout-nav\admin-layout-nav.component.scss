.content {
  padding: 1em 2em;
}

.hidden {
  display: none !important;
}

.v_divider {
  height: 30%;
}

.mat-toolbar {
  background-color: #fff;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
}

.toolbar-spacer {
  flex: 1 1 auto;
}

#lang_acronym {
  align-self: center;
  color: #1e2f41;
  padding-left: 0.5em;
}

#account_name {
  align-self: center;
  color: #1e2f41;
}

.line {
  width: 1px;
  height: 1.5em;
  border-left: 1px solid #e0e0e0;
}

.active {
  background-color: #f4eadf;
  color: #ce7a58 !important;
  border-radius: 25px 0 0 25px !important;
}

::ng-deep {
  .mat-tooltip {
    font-size: 13px;
  }

  a {
    text-decoration: none;
  }

  .mat-drawer-inner-container::-webkit-scrollbar {
    width: 0px;
    padding-left: 2em;
    background-color: transparent;
  }

  .main-container {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .main-is-mobile {
    .main-toolbar {
      position: fixed;
      z-index: 2;
    }

    .main-sidenav-container {
      flex: 1 0 auto;
    }
  }

  .main-app-name {
    padding: 0;
    margin-left: 8px;
    color: #1e2f41;

    .mat-icon {
      vertical-align: middle;
    }
  }

  .main-sidenav-container {
    flex: 1;

    mat-sidenav {
      background-color: #fff;
      color: #333;
      border: unset;
      box-shadow: 4px 0px 8px rgba(0, 0, 0, 0.1);
      background-position: 0px 70vh;
      background-size: 100%;
      background-repeat: no-repeat;
      max-height: 100% !important;
    }

    mat-sidenav-content {
      background-color: #f9f9f9;
      max-height: 100% !important;
      max-width: 100% !important;
    }
  }

  /*menu*/
  #sidebar_menu {
    margin-top: 0;
    padding: 0.3em 0;
    box-shadow: unset;
    background-color: transparent;

    span {
      padding: 0;
    }

    mat-panel-title {
      color: #1e2f41;
      font-weight: 500;
      display: initial;

      .mat-icon:nth-child(1) {
        transform: scale(1.1);
        margin-right: 0.3em;
        vertical-align: bottom;
      }
    }

    .mat-expansion-indicator {
      padding-bottom: 0.5em;

      &::after {
        color: #1e2f41;
      }
    }

    .mat-expansion-panel-body {
      padding: 0 !important;
    }

    #submenu {
      margin: 0 0 0 2.2em;
      width: 91%;
      text-align: left;
      padding: 0.2em 1em;

      .submenuTitle {
        display: block;
        width: 100%;
        line-height: 20px;
        white-space: pre-wrap;
        padding: 0.5em 0;
      }
    }

    a {
      color: #1e2f41;
    }

    mat-expansion-panel-header {
      padding: 0.6em;
    }

    .menuIcon_active {
      color: #ce7a58;
    }
  }

  .btn_accountMenu {
    padding: 0 1em;
    display: flex;

    #account_name {
      font-size: 16px;
      text-transform: capitalize;
      display: flex;

      .avatar {
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100%;
        // border: 1px solid rgba(0, 0, 0, 0.16);
        width: 2em;
        height: 2em;
        border-radius: 50px;
        margin-right: 0.5em;
      }
    }
  }

  .menu_apps {
    &.mat-menu-panel {
      min-width: 35em !important;

      .gridApps {
        display: flex;
        flex-wrap: wrap;

        .items {
          width: 30%;
          padding: 0.5em;

          .appLogo {
            width: 3.5em;
            height: 4em;
            background-position: center;
            background-repeat: no-repeat;
            background-size: 100%;
            margin-left: auto;
            margin-right: auto;
          }

          .appName {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #1e2f41;
            font-weight: 500;
            text-align: center;
          }
        }
      }
    }
  }

  .site_logo {
    a {
      display: flex;
      justify-content: center;
      background-color: #ce7a58;
      padding: 0.8em 0.5em;

      .logo_img {
        background-size: 100%;
        background-position: center;
        background-repeat: no-repeat;
        min-width: 3em;
      }

      .site_name {
        display: grid;

        span:nth-child(1) {
          font-size: 14px;
          font-weight: 400;
          padding: 0 0 0 0.7em;
          color: #fff;
          align-self: center;
        }

        span:nth-child(2) {
          text-transform: uppercase;
          font-size: 20px;
          font-weight: 500;
          padding: 0 0 0 0.5em;
          color: #fff;
          align-self: center;
          font-family: "Lora";
        }
      }
    }

    .close-button {
      top: 0;
      right: 0;
      position: absolute;
      color: #fff;
    }
  }

  .main-toolbar {
    .btn_toggleMenu {
      color: #ce7a58;
    }

    .btn_notiMenu {
      padding: 0 0.8em;
      color: #ce7a58;
      font-weight: 100;
      max-width: 5%;
      min-width: unset;
    }

    .btn_langMenu {
      padding: 0 0.8em;

      .mat-icon {
        vertical-align: middle;
        transform: scale(0.7);
      }
    }

    .btn_accountMenu {
      padding: 0 0.8em;
    }

    .btn_gridMenu {
      padding: 0 0.8em;
      max-width: 5%;
      min-width: unset;
    }

    .btn_resMenu {
      color: #1e2f41;
    }
  }

  .remindMenu {
    height: 35px;
    line-height: 35px;
    color: #1e2f41;
    display: flex;

    .remindName {
      min-width: 80%;
    }

    .remindNumber {
      min-width: 20%;
      background-color: #ce7a58 !important;
      padding: 2px 10px !important;
      border-radius: 15px !important;
      color: white !important;
      margin-right: 0.3em;
    }
  }

  .footer {
    margin-bottom: 0;
    top: 100%;
    position: sticky;
    height: 2em;
    box-shadow: 0px -5px 8px rgba(0, 0, 0, 0.1);

    .copyright {
      font-weight: 400;
      font-size: 14px;
      color: #1e2f41;

      .mat-icon {
        vertical-align: middle;
        transform: scale(0.9);
      }

      .itemRight {
        height: 32px;
      }
    }

    .developedBy {
      font-weight: 400;
      height: 32px;
      font-size: 14px;
      color: #1e2f41;
      display: flex;

      span {
        align-self: center;
        margin-right: 0.5em;
        padding-top: 0.2em;
      }

      .img {
        height: 2.8em;
        width: 7em;
        background-position: center;
        background-size: 100%;
        background-repeat: no-repeat;
      }
    }
  }
}
// ============================================= Responsive
@media screen and (max-width: 960px) {
  .content {
    max-height: 100% !important;
  }

  .site_logo {
    a {
      .site_name {
        display: grid;
        text-align: center;

        span:nth-child(1) {
          font-size: 14px;
          font-weight: 400;
          color: #fff;
        }

        span:nth-child(2) {
          text-transform: uppercase;
          font-size: 20px;
          font-weight: 500;
          color: #fff;
          font-family: "Lora";
        }
      }
    }

    .logo_img {
      background-size: 100%;
      background-position: center;
      background-repeat: no-repeat;
      width: 2em;
      height: 2em;
    }
  }
}

@media screen and (max-width: 600px) {
  .main-sidenav-container mat-sidenav {
    max-height: unset !important;
  }

  .content {
    margin-top: 4em;
    max-height: 100% !important;
    padding: 0.5em;
  }

  .res_accountName {
    font-weight: 500;
    color: #1e2f41;
  }

  ::ng-deep {
    .menu_apps {
      &.mat-menu-panel {
        min-width: 95vw !important;

        .gridApps {
          display: flex;
          flex-wrap: wrap;

          .items {
            width: 45%;
          }
        }
      }
    }
  }
}
