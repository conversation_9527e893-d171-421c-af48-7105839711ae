import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { FormService } from 'src/app/data/service/form/form.service';

export interface Form {
  id: string;
  code: string;
  name: string;
  status: number;
}

@Component({
  selector: 'app-add-form',
  templateUrl: './add-form.component.html',
  styleUrls: ['./add-form.component.scss']
})
export class AddFormComponent implements OnInit {

  addForm = new FormGroup({
    code: new FormControl(''),
    name: new FormControl(''),
    status: new FormControl('1')
  });
  savedName: string;
  constructor(
    public dialogRef: MatDialogRef<AddFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmAddFormDialogModel,
    private formService: FormService
  ) {
  }

  ngOnInit(): void {
  }

  onConfirm(): void {
    const formObj = this.addForm.getRawValue();
    this.savedName = formObj.name;

    // Delete element
    delete formObj.name;

    const frmName = [];
    frmName.push({ languageId: localStorage.getItem('languageId'), name: this.savedName });
    formObj.name = frmName;

    const resultJson = JSON.stringify(formObj, null, 2);
    this.postNewForm(resultJson);
  }

  onDismiss(): void {
    this.dialogRef.close();
  }

  // ========================================================== Manual function

  postNewForm(requestBody) {
    this.formService.postNewForm(requestBody).subscribe(data => {
      const result = {
        name: this.savedName,
        status: true
      };
      this.dialogRef.close(result);
    }, err => {
      const result = {
        name: this.savedName,
        status: false
      };
      this.dialogRef.close(result);
    });
  }

}

export class ConfirmAddFormDialogModel {
  constructor() {
  }
}
