<div fxLayout="row" fxLayoutAlign="center">
    <div class="breadcrumb" fxFlex.gt-sm="88" fxFlex="95">
        <a routerLink="/">Trang chủ</a>
        <mat-icon>navigate_next</mat-icon>
        <a routerLink="">Thống kê</a>
    </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_searchbar" fxFlex="grow" fxFlex.gt-sm="88" fxFlex="95" [ngStyle]="{'margin-left': '106px'}">
        <form class="searchForm" [formGroup]="searchForm">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">

                <mat-form-field appearance="outline" fxLayout="column" fxLayout.xs="column">
                    <mat-label>Cấp báo cáo</mat-label>
                    <mat-select #levelReportMatSelectInfinite formControlName="levelReportCtrl">
                        <!-- <mat-option *ngIf="showSelectAllLevelReport" class="hide-checkbox" value="" (click)="toggleAllSelectionLevelReport()">Tất cả</mat-option>                     -->
                        <mat-option (click)="optionClickLevelReport()" *ngFor="let item of listLevelReport"
                            value="{{item.id}}">
                            {{item.name}}
                            <span *ngIf="item.name == undefined || item.name == null || item.name.trim() == ''"
                                i18n>(Không tìm thấy bản dịch)</span>
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <div fxFlex='1'></div>
                <mat-form-field appearance="outline" fxLayout="column" fxLayout.xs="column">
                    <mat-label i18n="@@unit1">Đơn vị</mat-label>
                    <mat-select #subAgencyMatSelectInfinite formControlName="subAgencyCtrl" multiple>
                        <mat-option>
                            <ngx-mat-select-search [formControl]="searchSubAgencyCtrl" (keyup)="getListSubAgency(true)"
                                placeholderLabel="" i18n-noEntriesFoundLabel
                                noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                            </ngx-mat-select-search>
                        </mat-option>
                        <mat-option class="hide-checkbox" value="" (click)="toggleAllSelection()">Tất cả</mat-option>
                        <mat-option (click)="optionClick()" *ngFor="let agency of listSubAgencyFilteredSearch"
                            value="{{agency.id}}">
                            {{agency.name}}
                            <span *ngIf="agency.name == undefined || agency.name == null || agency.name.trim() == ''"
                                i18n>(Không tìm thấy bản dịch)</span>
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <div fxFlex='1'></div>
                <mat-form-field appearance="outline" fxLayout="column" fxLayout.xs="column">
                    <mat-label>Báo cáo theo</mat-label>
                    <mat-select [(value)]="reportTypeId"
                                (selectionChange)="onReportTypeChange($event)">
                    <mat-option value="0">Tổng hợp</mat-option>
                    <mat-option value="1" i18n>Lĩnh vực</mat-option>
                    </mat-select>
                </mat-form-field>

                <div fxFlex='1'></div>
                <mat-form-field appearance="outline" fxLayout="column" fxLayout.xs="column">
                    <mat-label i18n>Từ ngày</mat-label>
                    <input matInput [matDatepicker]="pickerFromDate" formControlName="fromDate">
                    <mat-datepicker-toggle matSuffix [for]="pickerFromDate"></mat-datepicker-toggle>
                    <mat-datepicker #pickerFromDate></mat-datepicker>
                </mat-form-field>
                <div fxFlex='1'></div>
                <mat-form-field appearance="outline" fxLayout="column" fxLayout.xs="column">
                    <mat-label i18n>Đến ngày</mat-label>
                    <input matInput [matDatepicker]="pickerToDate" formControlName="toDate">
                    <mat-datepicker-toggle matSuffix [for]="pickerToDate"></mat-datepicker-toggle>
                    <mat-datepicker #pickerToDate></mat-datepicker>
                </mat-form-field>
                <div fxFlex='1'></div>
                <div fxFlex='1'></div>
                <button mat-flat-button fxFlex.gt-sm="12" fxFlex='12' class="btn-search-modify" (click)="searchBtn()"
                    [disabled]="isLoading">
                    <mat-icon class="iconStatistical" *ngIf="!isLoading">bar_chart</mat-icon>
                    <mat-spinner diameter="25" class="iconStatistical" *ngIf="isLoading"></mat-spinner>
                    <span i18n>Thống kê</span>
                </button>
                <div fxFlex='1'></div>
                <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" *ngIf="reportTypeId === '2'">
                    <mat-form-field appearance="outline" class="searchKeyword">
                        <mat-label i18n>Thủ tục</mat-label>
                        <mat-select msInfiniteScroll [multiple]="true" [complete]="procedureData.last"
                            [(value)]="procedureData.selected" (infiniteScroll)="onControlNextBatch('procedure')">
                            <div>
                                <input class="search-nested" #searchInputProcedure matInput autocomplete="off"
                                    (keyup)="onControlSearch('procedure', $event)" (keydown)="$event.stopPropagation()"
                                    placeholder="Nhập từ khóa" />
                                <button mat-icon-button class="clear-search-nested" *ngIf="searchInputProcedure.value"
                                    (click)="searchInputProcedure.value = ''; onControlReset('procedure')">
                                    <mat-icon> close </mat-icon>
                                </button>
                            </div>
                            <!--<mat-option value="">Tất cả</mat-option>-->
                            <mat-option value="{{ item.id }}" checkboxPosition="before"
                                *ngFor="let item of procedureData.list" matTooltip="{{item.name ? item.name : null}}">
                                {{ item?.name ? item.name : '(Không tìm thấy bản dịch)' }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
        </form>
    </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
    <div fxFlex.gt-sm="88" fxFlex="95">
        <mat-tab-group class="st_tabs" (selectedTabChange)="tabChanged($event)">
            <mat-tab i18n-label label="Dạng bảng">
                <div class="frm_tbl0">
                    <div class="overload" id="overload" *ngIf="isLoading">
                        <div class="loading">
                            <div class="spinner-3"></div>
                        </div>
                    </div>                  
                    <table mat-table [dataSource]="dataSource" fxFlexOffset="5">                        
                        <!-- Header row once group -->
                        <ng-container matColumnDef="No1">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2">STT</th>
                        </ng-container>
                        <ng-container matColumnDef="No2">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2">{{excelColumnName}}</th>
                        </ng-container>
                        <ng-container matColumnDef="No3">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2">Tồn kỳ trước</th>
                        </ng-container>
                        <ng-container matColumnDef="No4">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3">Tiếp nhận</th>
                        </ng-container>
                        <ng-container matColumnDef="No5">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="5">Đã giải quyết</th>
                        </ng-container>
                        <ng-container matColumnDef="No6">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="5">Đang giải quyết</th>
                        </ng-container>
                        <ng-container matColumnDef="No7" *ngIf="enableModifiedGeneralReportQni">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2">Số lượng dịch vụ công được dùng</th>
                        </ng-container>
                        <!-- Header row second group -->
                        <ng-container matColumnDef="Num1">
                            <th mat-header-cell *matHeaderCellDef>Tổng tiếp nhận</th>
                        </ng-container>
                        <ng-container matColumnDef="Num2">
                            <th mat-header-cell *matHeaderCellDef>Tiếp nhận trực tuyến</th>
                        </ng-container>
                        <ng-container matColumnDef="Num3">
                            <th mat-header-cell *matHeaderCellDef>Tiếp nhận trực tiếp</th>
                        </ng-container>
                        <ng-container matColumnDef="Num4">
                            <th mat-header-cell *matHeaderCellDef>Tổng giải quyết</th>
                        </ng-container>
                        <ng-container matColumnDef="Num5">
                            <th mat-header-cell *matHeaderCellDef>Đúng hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="Num6">
                            <th mat-header-cell *matHeaderCellDef>Trễ hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="Num7">
                            <th mat-header-cell *matHeaderCellDef>Tỷ lệ đúng hạn (%)</th>
                        </ng-container>
                        <ng-container matColumnDef="Num8">
                            <th mat-header-cell *matHeaderCellDef>Tỷ lệ trễ hạn (%)</th>
                        </ng-container>

                        <ng-container matColumnDef="Num9">
                          <th mat-header-cell *matHeaderCellDef>Tổng Đang giải quyết</th>
                        </ng-container>
                        <ng-container matColumnDef="Num10">
                          <th mat-header-cell *matHeaderCellDef>Trong hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="Num11">
                          <th mat-header-cell *matHeaderCellDef>Quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="Num12">
                          <th mat-header-cell *matHeaderCellDef>Tỷ lệ trong hạn (%)</th>
                        </ng-container>
                        <ng-container matColumnDef="Num13">
                          <th mat-header-cell *matHeaderCellDef>Tỷ lệ quá hạn (%)</th>
                        </ng-container>
                        
                        <!-- Header row third group -->
                        <ng-container matColumnDef="Third1">
                            <th mat-header-cell *matHeaderCellDef>(1)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third2">
                            <th mat-header-cell *matHeaderCellDef>(2)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third3">
                            <th mat-header-cell *matHeaderCellDef>(3)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third4">
                            <th mat-header-cell *matHeaderCellDef>(4)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third5">
                            <th mat-header-cell *matHeaderCellDef>(5)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third6">
                            <th mat-header-cell *matHeaderCellDef>(6)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third7">
                            <th mat-header-cell *matHeaderCellDef>(7)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third8">
                            <th mat-header-cell *matHeaderCellDef>(8)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third9">
                            <th mat-header-cell *matHeaderCellDef>(9)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third10">
                            <th mat-header-cell *matHeaderCellDef>(10) = (8)/(7)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third11">
                            <th mat-header-cell *matHeaderCellDef>(11) = (9)/(7)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third12">
                            <th mat-header-cell *matHeaderCellDef>(12)</th>
                        </ng-container>

                        <ng-container matColumnDef="Third13">
                          <th mat-header-cell *matHeaderCellDef>(13)</th>
                        </ng-container>
                         <ng-container matColumnDef="Third14">
                          <th mat-header-cell *matHeaderCellDef>(14)</th>
                        </ng-container>
                         <ng-container matColumnDef="Third15">
                          <th mat-header-cell *matHeaderCellDef>(15) = (13)/(12)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third16">
                          <th mat-header-cell *matHeaderCellDef>(16) = (14)/(12)</th>
                        </ng-container>
                        <ng-container matColumnDef="Third17" *ngIf="enableModifiedGeneralReportQni">
                            <th mat-header-cell *matHeaderCellDef>(17)</th>
                        </ng-container>

                        <!-- Header row four group -->
                        <ng-container matColumnDef="stt">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">STT</th>
                            <td mat-cell *matCellDef="let row;">{{row.stt}}</td>
                            <td mat-footer-cell *matFooterCellDef></td>
                        </ng-container>
                        <ng-container matColumnDef="agency">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Lĩnh vực giải
                                quyết</th>
                            <td mat-cell *matCellDef="let row" class="cell_info" style="text-align: left;"
                                >
                                <a style="margin-left: 10px;">{{row.sectorName}}</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef>Tổng cộng</td>
                        </ng-container>
                        <ng-container matColumnDef="previousPeriod">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                            <td mat-cell *matCellDef="let row" class="cell_info">
                                <a>{{formatNumber(row.receivedOld)}}</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef class="cell_info">
                                <a>{{ formatNumber(sum('receivedOld')) }}</a>
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="generalReception">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng tiếp nhận
                            </th>
                            <td mat-cell *matCellDef="let row" class="cell_info">
                                <a>{{formatNumber(row.receivedModify)}}</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef class="cell_info">
                                <a>{{ formatNumber(sum('receivedModify')) }}</a>
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="onlineReception">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tiếp nhận trực tuyến
                            </th>
                            <td mat-cell *matCellDef="let row" class="cell_info" [ngStyle]="{'color': '#ce7a58'}">
                                <a>{{formatNumber(row.receivedOnline)}}</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef class="cell_info" [ngStyle]="{'color': '#ce7a58'}">
                                <a>{{ formatNumber(sum('receivedOnline')) }}</a>
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="directReception">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tiếp nhận trực tiếp</th>
                            <td mat-cell *matCellDef="let row" class="cell_info" [ngStyle]="{'color': 'blue'}">
                                <a>{{formatNumber(row.receivedDirect)}}</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef class="cell_info" [ngStyle]="{'color': 'blue'}">
                                <a>{{ formatNumber(sum('receivedDirect')) }}</a>
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="totalSettlement">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng giải quyết
                            </th>
                            <td mat-cell *matCellDef="let row" class="cell_info">
                                <a>{{formatNumber(row.resolved)}}</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef class="cell_info">
                                <a>{{ formatNumber(sum('resolved')) }}</a>
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="onTime">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Đúng hạn</th>
                            <td mat-cell *matCellDef="let row" class="cell_info">
                                <a>{{formatNumber(row.resolvedOnTimeModify)}}</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef class="cell_info">
                                <a>{{ formatNumber(sum('resolvedOnTimeModify')) }}</a>
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="late">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Trễ hạn</th>
                            <td mat-cell *matCellDef="let row" class="cell_info">
                                <a>{{formatNumber(row.resolvedOverdue)}}</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef class="cell_info">
                                <a>{{ formatNumber(sum('resolvedOverdue')) }}</a>
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="onTimeRate">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tỷ lệ đúng hạn
                            </th>
                            <td mat-cell *matCellDef="let row" class="cell_info">
                                <a>{{row.onTimeRate}} %</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef class="cell_info">
                                <a>{{ formatRateNumber(sum('onTimeRate'))}} %</a>
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="lateRate">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tỷ lệ trễ hạn</th>
                            <td mat-cell *matCellDef="let row" class="cell_info">
                                <a>{{formatNumber(row.lateRate)}} %</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef class="cell_info">
                                <a>{{ formatRateNumber(sum('lateRate')) }} %</a>
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="unresolved">
                          <th
                            mat-header-cell
                            *matHeaderCellDef
                            [ngStyle]="{ display: 'none' }"
                          >
                            Tổng Đang giải quyết
                          </th>
                          <td mat-cell *matCellDef="let row" class="cell_info">
                            <a>{{ formatNumber(row.unresolved) }}</a>
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="cell_info">
                            <a>{{formatNumber(sum("unresolved")) }}</a>
                          </td>
                        </ng-container>
            
                        <ng-container matColumnDef="unresolvedOnTime">
                          <th
                            mat-header-cell
                            *matHeaderCellDef
                            [ngStyle]="{ display: 'none' }"
                          >
                            Trong hạn
                          </th>
                          <td mat-cell *matCellDef="let row" class="cell_info">
                            <a>{{ formatNumber(row.unresolvedOnTime) }}</a>
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="cell_info">
                            <a>{{ formatNumber(sum("unresolvedOnTime")) }}</a>
                          </td>
                        </ng-container>
            
                        <ng-container matColumnDef="unresolvedOverdue">
                          <th
                            mat-header-cell
                            *matHeaderCellDef
                            [ngStyle]="{ display: 'none' }"
                          >
                            Quá hạn
                          </th>
                          <td mat-cell *matCellDef="let row" class="cell_info">
                            <a>{{ formatNumber(row.unresolvedOverdue) }}</a>
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="cell_info">
                            <a>{{ formatNumber(sum("unresolvedOverdue")) }}</a>
                          </td>
                        </ng-container>
            
                        <ng-container matColumnDef="unresolvedRate">
                          <th
                            mat-header-cell
                            *matHeaderCellDef
                            [ngStyle]="{ display: 'none' }"
                          >
                            Tỉ lệ đúng hạn
                          </th>
                          <td mat-cell *matCellDef="let row" class="cell_info">
                            <a>{{ formatNumber(row.unresolvedRate) }} %</a>
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="cell_info">
                            <a>{{ formatRateNumber(sum("unresolvedRate")) }} %</a>
                          </td>
                        </ng-container>
            
                        <ng-container matColumnDef="unresolvedOverdueRate">
                          <th
                            mat-header-cell
                            *matHeaderCellDef
                            [ngStyle]="{ display: 'none' }"
                          >
                            Tỉ lệ quá hạn
                          </th>
                          <td mat-cell *matCellDef="let row" class="cell_info">
                            <a>{{ formatNumber(row.unresolvedOverdueRate) }} %</a>
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="cell_info">
                            <a>{{ formatRateNumber(sum("unresolvedOverdueRate")) }} %</a>
                          </td>
                        </ng-container>
                        <ng-container matColumnDef="procedureUsed" *ngIf="enableModifiedGeneralReportQni">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                            <td mat-cell *matCellDef="let row" class="cell_info">
                                <a>{{formatNumber(row.procedureUsed)}}</a>
                            </td>
                            <td mat-footer-cell *matFooterCellDef class="cell_info">
                                <a>{{ formatNumber(sum('procedureUsed')) }}</a>
                            </td>
                        </ng-container>

                        <tr mat-header-row *matHeaderRowDef="getHeaderRowDef()"></tr>
                        <tr mat-header-row
                            *matHeaderRowDef="['Num1', 'Num2', 'Num3', 'Num4', 'Num5', 'Num6', 'Num7', 'Num8', 'Num9', 'Num10', 'Num11', 'Num12', 'Num13']">
                        </tr>
                        <tr mat-header-row
                            *matHeaderRowDef="getHeaderRowDefThird()">
                        </tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumnsModify;"></tr>
                        <tr mat-footer-row *matFooterRowDef="displayedColumnsModify; sticky: true"></tr>
                    </table>
                </div>
            </mat-tab>
            <mat-tab i18n-label label="Dạng biểu đồ">
                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="listAgency" [ngStyle]="{'margin-left': '30px'}">
                    <div class="st_item" fxFlex.gt-xs="60" fxFlex.gt-sm="47" fxFlex="95"
                        *ngFor="let item of pieListData">
                        <div class="head">
                            <p class="lableChart">{{titleLabelChartResolved}}</p>
                            <p class="lableChart">{{timeTitleFormatChart}}</p>
                        </div>
                        <div class="body">
                            <canvas baseChart style="background: #ffcc;" [data]=item.pieChartData
                                [labels]=item.pieChartLabels [chartType]="pieChartType" [options]="pieChartOptions"
                                [plugins]="pieChartPlugins" [legend]="pieChartLegend">
                            </canvas>
                        </div>
                    </div>

                    <div class="st_item" fxFlex.gt-xs="60" fxFlex.gt-sm="47" fxFlex="95"
                        *ngFor="let item of pieListDataProcessing">
                        <div class="head">
                            <p class="lableChart">{{titleLabelChartProcessing}}</p>
                            <p class="lableChart">{{timeTitleFormatChart}}</p>
                        </div>
                        <div class="body">
                            <canvas baseChart style="background: #ffcc;" [data]=item.pieChartData
                                [labels]=item.pieChartLabels [chartType]="pieChartType" [options]="pieChartOptions"
                                [plugins]="pieChartPlugins" [legend]="pieChartLegend">
                            </canvas>
                        </div>
                    </div>
                </div>

                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="listAgency">
                    <div class="st_item" fxFlex.gt-xs="120" fxFlex.gt-sm="120" fxFlex="95">
                        <div class="head">
                            <p class="lableChart">{{titleLabelChartBarResolved}}</p>
                            <p class="lableChart">{{timeTitleFormatChart}}</p>
                        </div>
                        <div class="body">
                            <canvas baseChart style="background: #ffcc;" [datasets]="barChartData"
                                [labels]="barChartLabels" [options]="barChartOptions" [plugins]="barChartPlugins"
                                [legend]="barChartLegend" [chartType]="barChartType">
                            </canvas>
                        </div>
                    </div>
                </div>

                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="listAgency">
                    <div class="st_item" fxFlex.gt-xs="120" fxFlex.gt-sm="120" fxFlex="95">
                        <div class="head">
                            <p class="lableChart">{{titleLabelChartBarProcessing}}</p>
                            <p class="lableChart">{{timeTitleFormatChart}}</p>
                        </div>
                        <div class="body">
                            <canvas baseChart style="background: #ffcc;" [datasets]="barChartDataProcessing"
                                [labels]="barChartLabelsProcessing" [options]="barChartOptionsProcessing" [plugins]="barChartPluginsProcessing"
                                [legend]="barChartLegendProcessing" [chartType]="barChartTypeProcessing">
                            </canvas>
                        </div>
                    </div>
                </div>
            </mat-tab>
        </mat-tab-group>
    </div>
</div>