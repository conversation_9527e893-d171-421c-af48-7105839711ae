import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { FormService } from 'src/app/data/service/form/form.service';

export interface Form {
  id: string;
  code: string;
  name: string;
  status: number;
}

@Component({
  selector: 'app-update-form',
  templateUrl: './update-form.component.html',
  styleUrls: ['./update-form.component.scss']
})
export class UpdateFormComponent implements OnInit {

  updateForm = new FormGroup({
    code: new FormControl(''),
    name: new FormControl(''),
    status: new FormControl('')
  });

  formId: string;
  response = [];
  savedName: string;

  constructor(
    public dialogRef: MatDialogRef<UpdateFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmUpdateFormDialogModel,
    private formService: FormService
  ) {
    this.formId = data.id;
  }

  ngOnInit(): void {
    this.getFormInfo(this.formId);
  }

  onConfirm(): void {
    const formObj = this.updateForm.getRawValue();
    this.savedName = formObj.name;

    // Delete element
    delete formObj.name;

    const frmName = [];
    frmName.push({ languageId: localStorage.getItem('languageId'), name: this.savedName });
    formObj.name = frmName;

    const resultJson = JSON.stringify(formObj, null, 2);
    this.putUpdateForm(resultJson);
  }

  onDismiss(): void {
    this.dialogRef.close();
  }

  // ========================================================== Manual function

  getFormInfo(id) {
    this.formService.getFormInfo(id).subscribe(data => {
      this.response.push(data);
      this.setViewData();
    });
  }

  setViewData() {
    this.updateForm = new FormGroup({
      code: new FormControl(this.response[0].code),
      name: new FormControl(this.response[0].name[0].name),
      status: new FormControl('' + this.response[0].status)
    });
  }

  putUpdateForm(requestBody) {
    this.formService.putUpdateForm(this.formId, requestBody).subscribe(data => {
      const result = {
        name: this.savedName,
        status: true
      };
      this.dialogRef.close(result);
    }, err => {
      const result = {
        name: this.savedName,
        status: false
      };
      this.dialogRef.close(result);
    });
  }

}

export class ConfirmUpdateFormDialogModel {
  constructor(public id: string) {
  }
}
