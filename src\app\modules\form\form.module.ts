import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { AdminLayoutModule } from 'src/app/layouts/admin/admin-layout.module';
import { FormRoutingModule } from './form-routing.module';
import { FormComponent } from './form.component';
import { AddFormComponent } from './dialogs/add-form/add-form.component';
import { UpdateFormComponent } from './dialogs/update-form/update-form.component';
import { DeleteFormComponent } from './dialogs/delete-form/delete-form.component';

@NgModule({
  declarations: [FormComponent, AddFormComponent, UpdateFormComponent, DeleteFormComponent],
  imports: [
    CommonModule,
    FormRoutingModule,
    SharedModule,
    AdminLayoutModule
  ]
})
export class FormModule { }
