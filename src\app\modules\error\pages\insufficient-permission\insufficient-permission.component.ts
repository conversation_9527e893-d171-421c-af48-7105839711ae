import { Component, OnInit } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { Router } from '@angular/router';

@Component({
  selector: 'app-insufficient-permission',
  templateUrl: './insufficient-permission.component.html',
  styleUrls: ['./insufficient-permission.component.scss', '../page-not-found/page-not-found.component.scss', '/src/app/app.component.scss']
})
export class InsufficientPermissionComponent implements OnInit {
  protected keycloakService: KeycloakService;

  constructor(keycloakService: KeycloakService, private router: Router) {
    this.keycloakService = keycloakService;
  }

  ngOnInit(): void {
    this.keycloakService.isLoggedIn().then(r => {
      if (!r) {
        this.router.navigate(['/']);
      }
    });
  }

  logout(): void {
    this.keycloakService.logout();
  }

}
