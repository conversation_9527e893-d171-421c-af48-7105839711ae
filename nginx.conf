worker_processes  1;

events {
    worker_connections  1024;
}

http {
    server {
        listen 80;
        server_name  localhost;

        root   /usr/share/nginx/html;
        index  index.html index.htm;
        include /etc/nginx/mime.types;

        gzip on;
        gzip_min_length 1000;
        gzip_proxied expired no-cache no-store private auth;
        gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;

        location /vi/ {
            alias   /usr/share/nginx/html/vi/;
            try_files $uri $uri/ /vi/index.html;
        }

        location /en/ {
            alias   /usr/share/nginx/html/en/;
            try_files $uri $uri/ /en/index.html;
        }

        location / {
            alias   /usr/share/nginx/html/vi/;
            try_files $uri $uri/ /vi/index.html;
        }
    }
}
