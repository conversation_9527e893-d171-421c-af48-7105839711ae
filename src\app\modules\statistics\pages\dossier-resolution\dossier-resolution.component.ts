import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormControl, FormBuilder } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { StatisticService } from 'src/app/data/service/statistics/statistic.service';
import { MatSelect } from '@angular/material/select';
import { MatOption } from '@angular/material/core';
import { MatTabChangeEvent } from "@angular/material/tabs";

export interface DossierStatistic {
  stt: number;
  agencyId: string;
  sectorId: string;
  sectorName: string;
  received: number;
  receivedOnline: number;
  receivedDirect: number;
  receivedOld: number;
  resolved: number;
  resolvedEarly: number;
  resolvedOnTime: number;
  resolvedOverdue: number;
  unresolved: number;
  unresolvedOnTime: number;
  unresolvedOverdue: number;
  direct: number;
  receivedPostal: number;
  receivedPublicPostal: number;
  procedureUsed: number;
}

@Component({
  selector: 'app-dossier-resolution',
  templateUrl: './dossier-resolution.component.html',
  styleUrls: ['./dossier-resolution.component.scss']
})
export class DossierResolutionComponent implements OnInit {
  dataSource: MatTableDataSource<DossierStatistic>;
  ELEMENTDATA: DossierStatistic[] = [];
  defaultColumns: string[] = ['stt', 'agency', 'previousPeriod', 'generalReception', 'onlineReception', 'directReception', 'totalSettlement', 'onTime', 'late', 'onTimeRate', 'lateRate', 'unresolved','unresolvedOnTime', 'unresolvedOverdue','unresolvedRate','unresolvedOverdueRate'];
  additionalColumns : string[] = ['procedureUsed'];
  enableModifiedGeneralReportQni = false;
  displayedColumnsModify : string[] = this.defaultColumns;

  searchForm: FormGroup;
  public searchSubAgencyCtrl: FormControl = new FormControl();
  listAgencySearch: any = [];
  reportTypeId = '1';
  excelColumnName = 'Lĩnh vực giải quyết';
  excelFileName = 'Thong ke ho so theo linh vuc';
  isLoading = false;
  isAgencyTreeOpen = false;
  savedItems = [];
  timeOutSearch = null;
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  msTimeOutSearch = 1000;
  listAllProcedure: any = [];
  sectorData = {
    list: [],
    backup: [],
    selected: [],
    keyword: '',
    page: 0,
    pageSize: 100,
    totalElement: 0,
    currentElement: 0,
    last: false,
    disabled: false,
  };

  procedureData = {
    list: [],
    selected: [],
    backup: [],
    keyword: '',
    page: 0,
    pageSize: 100,
    totalElement: 0,
    currentElement: 0,
    last: false,
    disabled: false,
  };

  listLevelReport: any = [
    {
      id: 1,
      name: "Cấp Sở"
    },
    {
      id: 2,
      name: "Cấp Huyện"
    },
    {
      id: 3,
      name: "Cấp Xã"
    },
    {
      id: 4,
      name: "Đất Đai"
    },
  ];
  datas = {
    levelReport: { id: 1, name: "Cấp Sở" }
  };

  showSelectAllLevelReport: boolean = true;
  @ViewChild('levelReportMatSelectInfinite', { static: true }) levelReportMatSelect: MatSelect;
  @ViewChild('subAgencyMatSelectInfinite', { static: true }) subAgencyMatSelect: MatSelect;
  allSelectedSubAgency = false;
  allSelectedlevelReport = false;
  xpandStatus: boolean = false;
  listSubAgencyFilteredSearch: any = [];
  listSubAgency: any = [];
  isPermissionDisplayAdvanced: boolean = false;
  selectedLevelReport = "";
  showTable: boolean;
  currentTab: number;
  titleLabelChartResolved = '';
  titleLabelChartProcessing = '';
  titleLabelChartBarResolved = '';
  titleLabelChartBarProcessing = '';
  pieListData = [];
  pieListDataProcessing = [];
  timeTitleFormatChart: any;
  enableChangeReporter = false;
  constructor(
    private snackbarService: SnackbarService,
    private statisticService: StatisticService,
    private datePipe: DatePipe,
    private fb: FormBuilder
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.selectedLevelReport = "1";
  }

  async ngOnInit(): Promise<void> {
    this.searchForm = this.fb.group({
      fromDate: [""],
      toDate: [""],
      agency: [""],
      agencyCtrl: [""],
      levelReportCtrl: ["1"],
      subAgencyCtrl: [""],
    });
    await this.getListSubAgency(false);
    const allSelectedValues = this.listSubAgencyFilteredSearch.map(item => item.id);
    this.searchForm.get("subAgencyCtrl").setValue(allSelectedValues);
    this.allSelectedSubAgency = true;
    const d = new Date();
    this.searchForm.patchValue({
      fromDate: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + '01',
      toDate: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('0' + d.getDate()).slice(-2)
    });
    this.showTable = false;
    this.searchBtn();
  }

  tabChanged(tabChangeEvent: MatTabChangeEvent) {
    this.currentTab = tabChangeEvent.index;
    this.showTable = this.currentTab !== 0;
  }

  async searchBtn() {
    const formObj = this.searchForm.getRawValue();

    if (formObj.toDate === '' || formObj.fromDate === '') {
      const message = {
        vi: 'Ngày thống kê không được để trống!',
        en: 'Statistical date cannot be blank!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    } else if (new Date(formObj.toDate) < new Date(formObj.fromDate)) {
      const message = {
        vi: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
        en: 'Start date must be less than or equal to end date!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    }

    this.isLoading = true;
    this.isAgencyTreeOpen = false;
    let listAllSector = [];
    let listAllProcedure = [];
    let listAgencys = [];
    let isDvc = this.enableModifiedGeneralReportQni;
    if(this.reportTypeId !== "0"){
      isDvc = false;
    }

    let listAgencyReport = [];
    if (!this.xpandStatus) {
      listAgencyReport = [...this.savedItems]
    } else {
      let agencys = formObj.subAgencyCtrl;
      if (agencys.length != 0) {
        listAgencyReport = [...agencys];
      }
      else if (this.listSubAgency.length != 0)
        listAgencyReport = [...this.listSubAgency]
    }

    if (listAgencyReport.length == 0) {
      // Default agency ID - hardcoded for now
      listAgencys = ['default-agency-id'];
    } else {
      listAgencys = [...listAgencyReport];
    }

    if (this.reportTypeId === '1' && this.sectorData.selected.length == 0) {
      listAllSector = []
    } else
      listAllSector = [...this.sectorData.selected]

    if (this.reportTypeId === '2' && this.procedureData.selected.length == 0) {
      listAllProcedure = []
    }
    else
      listAllProcedure = [...this.procedureData.selected]

    listAgencys = listAgencys.filter(function (element) {
      return element !== undefined;
    });
    let agencysModify = formObj.subAgencyCtrl.filter(elements => {
      return (elements != null && elements !== undefined && elements !== "");
    });
    this.listAgencySearch = [...agencysModify];
    listAgencys = [...agencysModify];
    const searchString: string = '?agency-id=' + listAgencys.join(',') + '&report-type-id=' + this.reportTypeId
    + '&from-date=' + this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z'
    + '&to-date=' + this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z'
    + '&sector-id=' + listAllSector.join(',')
    + '&procedure-id=' + listAllProcedure.join(',') + '&is-dvc=' + isDvc;
    this.ELEMENTDATA = [];
    let data: any = [];

    if (this.enableChangeReporter) {
       data = await this.statisticService.getDossierStatisticGeneralFromReporter(searchString).toPromise();
    }else{
      data = await this.statisticService.getDossierStatisticGeneral(searchString).toPromise();
    }

    if (data) {
      let sectorId;
      let sectorName;
      let stt = 1;
      for (let i = 0; i < data.length; i++) {
        switch (this.reportTypeId) {
          case '1': {
            sectorId = data[i]?.sector?.id;
            sectorName = data[i]?.sector?.name;
            break;
          }
          case '2': {
            sectorId = data[i]?.procedure?.id;
            sectorName = data[i]?.procedure?.name;
            break;
          }
          default: {
            sectorId = data[i]?.agency?.id;
            sectorName = data[i]?.agency?.name;
            break;
          }
        }
        let obj = {
          stt: stt,
          sectorId,
          sectorName,
          agencyId: data[i]?.agency?.id,
          received: data[i].received + data[i].receivedOld,
          receivedOnline: data[i].receivedOnline,
          receivedDirect: data[i].receivedDirect,
          receivedOld: data[i].receivedOld,
          resolved: data[i].resolved,
          resolvedEarly: data[i].resolvedEarly,
          resolvedOnTime: data[i].resolvedOnTime,
          resolvedOverdue: data[i].resolvedOverdue,
          unresolved: data[i].unresolved,
          unresolvedOnTime: data[i].unresolvedOnTime,
          unresolvedOverdue: data[i].unresolvedOverdue,
          direct: data[i].receivedDirect - data[i].receivedPostal - data[i].receivedPublicPostal,
          receivedPostal: data[i].receivedPostal,
          receivedPublicPostal: data[i].receivedPublicPostal,
          receivedSmartphone: data[i].receivedSmartphone,
          withdraw: data[i].withdraw,
          onTimeRate: 0, // tỷ lệ đúng hạn
          lateRate: data[i].resolved > 0 ? Math.round(((data[i].resolvedOverdue / data[i].resolved) * 100) * 10) / 10 : 0, // tỷ lệ trễ hạn
          receivedModify: data[i].receivedOnline + data[i].receivedDirect, // tổng tiếp nhận
          resolvedOnTimeModify: data[i].resolvedEarly + data[i].resolvedOnTime,
          unresolvedRate: data[i].unresolved > 0 ? Math.round(((data[i].unresolvedOnTime / data[i].unresolved) * 100) * 10) / 10 : 0,
          unresolvedOverdueRate: data[i].unresolved > 0 ? Math.round(((data[i].unresolvedOverdue / data[i].unresolved) * 100) * 10) / 10 : 0,
          procedureUsed: data[i].procedureUsed ? data[i].procedureUsed : 0
        }

        obj.onTimeRate = data[i].resolved > 0 ? Math.round((parseInt(obj.resolvedOnTimeModify) / parseInt(obj.resolved) * 100) * 10) / 10 : 0
        if (!this.checkZeroData(obj)) {
          this.ELEMENTDATA.push(obj);
          stt++;
        }

      }
    }

    this.dataSource.data = this.ELEMENTDATA;
    this.isLoading = false;

    // mapping chart
    this.timeTitleFormatChart = `Từ ngày: ${this.datePipe.transform(formObj.fromDate, 'dd-MM-yyyy')} đến ngày: ${this.datePipe.transform(formObj.toDate, 'dd-MM-yyyy')}`.toUpperCase();
    this.titleLabelChartResolved = `Thống kê hồ sơ đã xử lý ${this.listLevelReport.filter(x => x.id == formObj.levelReportCtrl)[0].name}`.toUpperCase();
    this.titleLabelChartProcessing = `Thống kê hồ sơ đang xử lý ${this.listLevelReport.filter(x => x.id == formObj.levelReportCtrl)[0].name}`.toUpperCase();
    this.titleLabelChartBarResolved = `Thống kê hồ sơ đã xử lý ${this.listLevelReport.filter(x => x.id == formObj.levelReportCtrl)[0].name}`.toUpperCase();
    this.titleLabelChartBarProcessing = `Thống kê hồ sơ đang xử lý ${this.listLevelReport.filter(x => x.id == formObj.levelReportCtrl)[0].name}`.toUpperCase();
  }

  async onControlSearch(type, event) {
    setTimeout(async () => {
      const keyword = event?.target?.value;
      switch (type) {
        case 'sector': {
          await this.onControlReset('sector', false);
          if (event) {
            event.preventDefault();
            this.sectorData.keyword = keyword;
          }
          this.sectorData.list = this.sectorData.backup.filter(e => e.name.includes(keyword) || e.id.includes(keyword));
          break;
        }
        case 'procedure': {
          await this.onControlReset('procedure', false);
          if (event) {
            event.preventDefault();
            this.procedureData.keyword = event.target.value;
          }
          this.procedureData.list = this.procedureData.backup.filter(e => e.name.includes(keyword) || e.id.includes(keyword));
          break;
        }
      }
    }, 300);
  }

  getListSector(pageState) {
    let idTemp = this.getListAgencyReport();
    this.statisticService.getRootAgencies(`?id=${idTemp.join(',')}`).subscribe(rootAgencies => {
      const { keyword, page, pageSize } = pageState;
      if (rootAgencies && rootAgencies.length) {
        const searchString = `?arr-agency-id=${rootAgencies.map(e => e.id).join(',')}`;
        this.statisticService.getListSectorsByAgencyQNI(searchString).subscribe(data => {
          this.sectorData.last = true;
          this.sectorData.totalElement = data.length;
          if (page === 0) {
            this.sectorData.list = data;
          } else {
            this.sectorData.list = this.sectorData.list.concat(data);
          }
          this.sectorData.backup = [...this.sectorData.list];
        }, err => {
          console.log(err);
        });
      }
    }, err => {
      console.log(err);
    });
  }
  getHeaderRowDef(): string[] {
    return ['No1', 'No2', 'No3', 'No4', 'No5', 'No6'];
  }

  getHeaderRowDefThird(): string[] {
    return ['Third1', 'Third2', 'Third3', 'Third4', 'Third5', 'Third6', 'Third7', 'Third8', 'Third9', 'Third10', 'Third11', 'Third12', 'Third13', 'Third14', 'Third15', 'Third16'];
  }

  getListProcedure(pageState: any) {
    let idTemp = this.getListAgencyReport();
    this.statisticService.getRootAgencies(`?id=${idTemp.join(',')}`).subscribe(rootAgencies => {
      const { page } = pageState;
      if (rootAgencies && rootAgencies.length) {
        const searchString = `?agency-id=${rootAgencies.map((e: any) => e.id).join(',')}`;
        this.statisticService.getListProcedureByAgency(searchString).subscribe(data => {
          this.procedureData.last = true;
          this.procedureData.totalElement = data.numberOfElements;
          if (page === 0) {
            this.procedureData.list = data.content;
          } else {
            this.procedureData.list = this.procedureData.list.concat(data.content);
          }
          this.procedureData.backup = [...this.procedureData.list];
        }, err => {
          console.log(err);
        });
      }
    });
  }

  agencyTreeChange(obj: any) {
    if (Array.isArray(obj)) {
      this.savedItems = obj.map(item => item.id);
      switch (this.reportTypeId) {
        case '1': {
          this.onControlReset('sector');
          break;
        }
        case '2': {
          this.onControlReset('procedure');
          break;
        }
      }
    }
  }

  onReportTypeChange(event: any) {
    switch (event?.value) {
      case '0': {
        this.excelColumnName = 'Đơn vị';
        this.excelFileName = 'Thong ke ho so theo don vi';
        break;
      }
      case '2': {
        this.onControlReset('procedure');
        this.sectorData.selected = [];
        this.excelColumnName = 'Thủ tục hành chính';
        this.excelFileName = 'Thong ke ho so theo thu tuc ';
        break;
      }
      default: {
        this.onControlReset('sector');
        this.procedureData.selected = [];
        this.excelColumnName = 'Lĩnh vực giải quyết';
        this.excelFileName = 'Thong ke ho so theo linh vuc ';
        break;
      }
    }
  }

  onTreeOpenChange(value: boolean) {
    this.isAgencyTreeOpen = value;
  }

  async onControlNextBatch(type) {
    clearTimeout(this.timeOutSearch);
    this.timeOutSearch = setTimeout(async () => {
      switch (type) {
        case 'sector': {
          if (!this.sectorData.last) {
            this.sectorData.page++;
            await this.getListSector(this.sectorData);
          }
          break;
        }
        case 'procedure': {
          if (!this.procedureData.last) {
            this.procedureData.page++;
            await this.getListProcedure(this.procedureData);
            await this.getListProcedureAll();
          }
          break;
        }
      }
    }, this.msTimeOutSearch);
  }

  async onControlReset(type, force = true) {
    const initPage = {
      page: 0,
      list: [],
      keyword: '',
      totalElement: 0,
    };
    switch (type) {
      case 'sector': {
        this.sectorData = {
          ...this.sectorData,
          ...initPage,
        };
        if (force) {
          await this.getListSector(this.sectorData);
        }
        break;
      }
      case 'procedure': {
        this.procedureData = {
          ...this.procedureData,
          ...initPage,
        };
        if (force) {
          await this.getListProcedure(this.procedureData);
          await this.getListProcedureAll();
        }
        break;
      }
    }
  }

  getListProcedureAll() {
    let idTemp = this.getListAgencyReport();
    this.statisticService.getRootAgencies(`?id=${idTemp.join(',')}`).subscribe(rootAgencies => {
      if (rootAgencies && rootAgencies.length) {
        const searchString = `?agency-id=${rootAgencies.map(e => e.id).join(',')}`;
        this.statisticService.getListProcedureByAgencyQNI(searchString).subscribe(data => {
          this.listAllProcedure = data.content;
        }, err => {
          console.log(err);
        });
      }
    });
  }

  //SRTART IGATESUPP-42249
  onClickOpenAdvancedSearchBox() {
    this.xpandStatus = !this.xpandStatus;
  }
  async optionClickLevelReport() {
    let newStatus = true;
    this.levelReportMatSelect.options.forEach((item: MatOption) => {
      if (!item.selected) {
        newStatus = false;
      }
      if (item.value === '') {
        item.deselect();
      }
    });
    this.allSelectedlevelReport = newStatus;
    await this.getListSubAgency(false);
    const allSelectedValues = this.listSubAgencyFilteredSearch.map(item => item.id);
    this.searchForm.get("subAgencyCtrl").setValue(allSelectedValues);
  }

  toggleAllSelectionLevelReport() {
    this.allSelectedlevelReport = !this.allSelectedlevelReport;  // to control select-unselect
    if (this.allSelectedlevelReport) {
      this.levelReportMatSelect.options.forEach((item: MatOption) => {
        if (!item.selected) {
          item.select();
        }
        if (item.value === '') {
          item.deselect();
        }
      });
    } else {
      this.levelReportMatSelect.options.forEach((item: MatOption) => item.deselect());
    }
  }

  async getListSubAgency(isSearch) {
    if (isSearch) {
      const search = this.searchSubAgencyCtrl.value;
      this.listSubAgencyFilteredSearch = this.listSubAgency.filter(x => x.name.toLowerCase().includes(search.toLowerCase()));
    } else {
      const formObj = this.searchForm.getRawValue();
      let searchString = "?ancestor-id=default-agency-id";
      let tag_id = "&tag-id=";

      // Hardcoded level tag IDs
      const levelTagID = {
        capSo: 'cap-so-tag',
        capHuyen: 'cap-huyen-tag',
        capXa: 'cap-xa-tag',
        daiDai: 'dat-dai-tag'
      };

      switch (formObj.levelReportCtrl) {
        case '1': tag_id += levelTagID.capSo + ","; break;
        case '2': tag_id += levelTagID.capHuyen + ","; break;
        case '3': tag_id += levelTagID.capXa + ","; break;
        case '4': tag_id += levelTagID.daiDai + ","; break;
        default: tag_id += levelTagID.capSo + ","; break;
      }
      tag_id = tag_id.substring(0, tag_id.length - 1);
      searchString += tag_id;

      const data: any = await this.statisticService.getListAgencyByTagId(searchString).toPromise();
      this.listSubAgency = [...data]
      this.listSubAgencyFilteredSearch = [...this.listSubAgency]
    }
  }

  toggleAllSelection() {
    this.allSelectedSubAgency = !this.allSelectedSubAgency;  // to control select-unselect
    if (this.allSelectedSubAgency) {
      this.subAgencyMatSelect.options.forEach((item: MatOption) => {
        if (!item.selected) {
          item.select();
        }
        if (item.value === '') {
          item.deselect();
        }
      });
    } else {
      this.subAgencyMatSelect.options.forEach((item: MatOption) => item.deselect());
    }
  }

  optionClick() {
    let newStatus = true;
    this.subAgencyMatSelect.options.forEach((item: MatOption) => {
      if (!item.selected) {
        newStatus = false;
      }
      if (item.value === '') {
        item.deselect();
      }
    });
    this.allSelectedSubAgency = newStatus;
  }

  getListAgencyReport() {
    let idTemp = [];
    if (!this.xpandStatus) {
      if (this.savedItems.length == 0) {
        idTemp = ['default-agency-id'];
      }
      else
        idTemp = [...this.savedItems]
    } else {
      const formObj = this.searchForm.getRawValue();
      let agencys = formObj.subAgencyCtrl;
      if (agencys.length != 0) {
        idTemp = agencys;
      }
      else if (this.listSubAgency.length != 0)
        idTemp = [...this.listSubAgency]
      else
        idTemp = ['default-agency-id'];
    }
    idTemp = idTemp.filter(function (element) {
      return element !== undefined;
    });
    return idTemp;
  }

  formatNumber(n: number) {
    return (n == null || false) ? '' : n.toString().replace(/(.)(?=(\d{3})+$)/g, '$1.');
  }

  formatRateNumber(n: number) {
    return (n == null || false) ? '' : n.toString().replace(/(.)(?=(\d{3})+$)/g, '$1.').replace('.',',');
  }

  sum(key: keyof any) {
    const calculateRate = (key: string) => {
      let nonZeroValues = this.dataSource.data.filter(item => Number(item[key]) !== 0);
      let temp = nonZeroValues.reduce((a, b) => a + (Number(b[key]) || 0), 0);
      return nonZeroValues.length > 0 ? (Math.round((temp / nonZeroValues.length) * 10) / 10) : 0;
    };
    const calculateRemainingRate = (rateKey: string) => {
        return Math.round((100 - this.sum(rateKey)) * 10) / 10;
    };
    switch (key) {
        case "onTimeRate":
            return calculateRate('onTimeRate');
        case "lateRate":
            return calculateRemainingRate("onTimeRate");
        case "unresolvedRate":
            return calculateRate('unresolvedRate');
        case "unresolvedOverdueRate":
            return calculateRemainingRate("unresolvedRate");
        default:
            return this.dataSource.data.reduce((a, b) => a + (Number(b[key]) || 0), 0);
    }
  }

  checkZeroData(obj: any) {
    try {
      if (obj.received == 0 &&
        obj.receivedOnline == 0 &&
        obj.receivedDirect == 0 &&
        obj.receivedOld == 0 &&
        obj.resolved == 0 &&
        obj.resolvedEarly == 0 &&
        obj.resolvedOnTime == 0 &&
        obj.resolvedOverdue == 0 &&
        obj.unresolved == 0 &&
        obj.unresolvedOnTime == 0 &&
        obj.unresolvedOverdue == 0 &&
        obj.direct == 0 &&
        obj.receivedPostal == 0 &&
        obj.receivedPublicPostal == 0 &&
        obj.receivedSmartphone == 0 && //fix bug
        obj.withdraw == 0)
        return true
      return false;
    }
    catch (e) {
      return false;
    }
  }
}
