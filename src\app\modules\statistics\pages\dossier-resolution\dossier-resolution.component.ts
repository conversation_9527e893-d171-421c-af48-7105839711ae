import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-dossier-resolution',
  templateUrl: './dossier-resolution.component.html',
  styleUrls: ['./dossier-resolution.component.scss']
})
export class DossierResolutionComponent implements OnInit {

  constructor() { }

  ngOnInit(): void {
  }

}
import { DatePipe } from '@angular/common';
import { Component, HostListener, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { ProcedureService } from 'data/service/procedure/procedure.service';
import { QNIStatisticService } from 'src/app/data/service/qni-statistics/qni-statistic.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { AgencyTreeComponent } from 'modules/padsvc/statistic-general/component/agency-tree/agency-tree.component';
import { BasedataService } from 'src/app/data/service/svc-basedata/basedata.service';
import { MatSelect } from '@angular/material/select';
import { MatOption } from '@angular/material/core';
import { MatTabChangeEvent } from "@angular/material/tabs";

//--Pie Chart DBN ------//
import { ChartType, ChartOptions, ChartDataSets } from 'chart.js';
import { MultiDataSet, SingleDataSet, Label, monkeyPatchChartJsLegend, monkeyPatchChartJsTooltip } from 'ng2-charts';
//--END Pie Chart DBN ------//

export interface DossierStatistic {
  stt: number;
  agencyId: string;
  sectorId: string;
  sectorName: string;
  received: number;          // tiepNhanTrongKy
  receivedOnline: number;    // tiepNhanTrucTuyen
  receivedDirect: number;    // tiepNhanTrucTiep
  receivedOld: number;       // kyTruocChuyenSang
  resolved: number;          // daXuLy
  resolvedEarly: number;     // daXuLyTruocHan
  resolvedOnTime: number;    // daXuLyDungHan
  resolvedOverdue: number;   // daXuLyQuaHan
  unresolved: number;        // dangXuLy
  unresolvedOnTime: number;  // dangXuLyTrongHan
  unresolvedOverdue: number; // dangXuLyQuaHan
  direct: number; // trucTiep
  receivedPostal: number; // buuChinh
  receivedPublicPostal: number; // buuChinhCongIch
  procedureUsed: number; 
}

@Component({
  selector: 'app-statistic-general',
  templateUrl: './statistic-general-qni.component.html',
  styleUrls: ['./statistic-general-qni.component.scss']
})
////BÁO CÁO CHUNG ĐẶC THÙ QNI-- KHÔNG ĐƯỢC CLONE DÙNG CŨNG KHÔNG ĐƯỢC ĐÂU 
export class StatisticGeneralComponent implements OnInit {
  dataSource: MatTableDataSource<DossierStatistic>;
  ELEMENTDATA: DossierStatistic[] = [];  
  defaultColumns: string[] = ['stt', 'agency', 'previousPeriod', 'generalReception', 'onlineReception', 'directReception', 'totalSettlement', 'onTime', 'late', 'onTimeRate', 'lateRate', 'unresolved','unresolvedOnTime', 'unresolvedOverdue','unresolvedRate','unresolvedOverdueRate'];
  additionalColumns : string[] = ['procedureUsed'];
  env = this.deploymentService.getAppDeployment()?.env;
  enableModifiedGeneralReportQni = this.deploymentService.env?.OS_QNI?.enableModifiedGeneralReportQni || false;
  displayedColumnsModify : string[] = this.enableModifiedGeneralReportQni ? this.defaultColumns.concat(this.additionalColumns) : this.defaultColumns;

  searchForm: FormGroup;
  public searchSubAgencyCtrl: FormControl = new FormControl();
  listAgencySearch: any = [];
  reportTypeId = '1';
  excelColumnName = 'Lĩnh vực giải quyết';
  excelFileName = 'Thong ke ho so theo linh vuc';
  isLoading = false;
  isAgencyTreeOpen = false;
  savedItems = [];
  timeOutSearch = null;
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  msTimeOutSearch = this.deploymentService.env.statistics.msTimeOutSearch;
  listAllProcedure: any = [];
  sectorData = {
    list: [],
    backup: [],
    selected: [],
    keyword: '',
    page: 0,
    pageSize: 100,
    totalElement: 0,
    currentElement: 0,
    last: false,
    disabled: false,
  };

  procedureData = {
    list: [],
    selected: [],
    backup: [],
    keyword: '',
    page: 0,
    pageSize: 100,
    totalElement: 0,
    currentElement: 0,
    last: false,
    disabled: false,
  };

  listLevelReport: any = [
    {
      id: 1,
      name: "Cấp Sở"
    },
    {
      id: 2,
      name: "Cấp Huyện"
    },
    {
      id: 3,
      name: "Cấp Xã"
    },
    {
      id: 4,
      name: "Đất Đai"
    },
  ];
  datas = {
    levelReport: { id: 1, name: "Cấp Sở" }
  };

  fgForm: FormGroup;
  fgFormAgency: FormGroup;
  showSelectAllLevelReport: boolean = true;
  @ViewChild('levelReportMatSelectInfinite', { static: true }) levelReportMatSelect: MatSelect;
  @ViewChild('subAgencyMatSelectInfinite', { static: true }) subAgencyMatSelect: MatSelect;
  allSelectedSubAgency = false;
  allSelectedlevelReport = false;
  xpandStatus: boolean = false;
  listSubAgencyFilteredSearch: any = [];
  listSubAgency: any = [];
  isPermissionDisplayAdvanced: boolean = false;
  selectedLevelReport = "";
  showTable: boolean;
  currentTab: number;
  titleLabelChartResolved = '';
  titleLabelChartProcessing = '';
  titleLabelChartBarResolved = '';
  titleLabelChartBarProcessing = '';
  pieChartLegend = true;
  pieChartPlugins = [];
  //--Pie chart
  pieChartOptions: ChartOptions = {
    responsive: true,
  };
  pieChartType: ChartType = 'pie';
  //--Doughnut chart
  doughnutChartLabels: Label[] = ['Sớm hạn: 0', 'Đúng hạn: 0', 'Trễ hạn: 0', 'Đã hủy: 0', 'Đang xử lý: 0'];
  doughnutChartData: MultiDataSet = [
    [25, 25, 25, 25, 25]
  ];
  pieListData = [];
  pieListDataProcessing = [];
  barChartData: ChartDataSets[] = [

  ];
  barChartDataProcessing: ChartDataSets[] = [

  ];
  barChartLabels: Label[] = [];
  barChartLabelsProcessing: Label[] = [];
  onTimeMth = [];
  overdueMth = [];
  earlyMth = [];
  onTimeMthProcessing = [];
  overdueMthProcessing = [];
  earlyMthProcessing = [];
  barChartType: ChartType = 'bar';
  barChartTypeProcessing: ChartType = 'bar';
  barChartLegend = true;
  barChartLegendProcessing = true;
  barChartOptions: ChartOptions = {
    responsive: true,
  };
  barChartOptionsProcessing: ChartOptions = {
    responsive: true,
  };
  barChartPlugins = [];
  barChartPluginsProcessing = [];
  timeTitleFormatChart: any;
  //Tham so thay doi api xem bao cao tu reporter
  enableChangeReporter = this.deploymentService.env.OS_QNI.enableChangeReporter || false;
  constructor(
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    private qniStatisticsService: QNIStatisticService,    
    private datePipe: DatePipe,    
    private basedataService: BasedataService,
    private fb: FormBuilder
  ) {
    monkeyPatchChartJsTooltip();
    monkeyPatchChartJsLegend();
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.selectedLevelReport = "1";

  }

  async ngOnInit(): Promise<void> {
    this.searchForm = this.fb.group({
      fromDate: [""],
      toDate: [""],
      agency: [""],
      agencyCtrl: [""],
      levelReportCtrl: ["1"],
      subAgencyCtrl: [""],
    });
    await this.getListSubAgency(false);
    const allSelectedValues = this.listSubAgencyFilteredSearch.map(item => item.id);
    this.searchForm.get("subAgencyCtrl").setValue(allSelectedValues);
    this.allSelectedSubAgency = true;
    const d = tUtils.newDate();
    this.searchForm.patchValue({
      fromDate: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + '01',
      toDate: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('0' + d.getDate()).slice(-2)
    });
    this.showTable = false;
    this.searchBtn();
  }

  tabChanged(tabChangeEvent: MatTabChangeEvent) {
    this.currentTab = tabChangeEvent.index;
    this.showTable = this.currentTab !== 0;
  }

  fetchDataBarChart() {
    var data = this.dataSource.data;
    for (let i = 0; i < this.dataSource.data.length; i++) {
      this.barChartLabels[i] = data[i].sectorName;
      this.onTimeMth[i] = data[i].resolvedOnTime;
      this.overdueMth[i] = data[i].resolvedOverdue;
      this.earlyMth[i] = data[i].resolvedEarly;
    }
  }

  fetchDataBarChartProcessing() {
    var data = this.dataSource.data;
    for (let i = 0; i < this.dataSource.data.length; i++) {
      this.barChartLabelsProcessing[i] = data[i].sectorName;
      this.onTimeMthProcessing[i] = data[i].unresolvedOnTime;
      this.overdueMthProcessing[i] = data[i].unresolvedOverdue;
    }
  }

  getDataChartBar() {
    this.barChartLabels = [];
    this.barChartData = [];
    this.onTimeMth = [];
    this.overdueMth = [];
    this.fetchDataBarChart();

    this.barChartData.push({ data: this.onTimeMth, label: 'Đúng hạn' });
    this.barChartData.push({ data: this.overdueMth, label: 'Quá hạn' });
    this.barChartData.push({ data: this.earlyMth, label: 'Sớm hạn' });
  }

  getDataChartBarProcessing() {
    this.barChartLabelsProcessing = [];
    this.barChartDataProcessing = [];
    this.onTimeMthProcessing = [];
    this.fetchDataBarChartProcessing();

    this.barChartDataProcessing.push({ data: this.onTimeMthProcessing, label: 'Trong hạn' });
    this.barChartDataProcessing.push({ data: this.overdueMthProcessing, label: 'Quá hạn' });
  }

  fetchDataPieChar() {
    this.pieListData = [];
    const pieData = {
      nameLevelAgency: '',
      indexArr: 0,
      pieChartLabels: ['Sớm hạn', 'Đúng hạn', 'Trễ hạn'],
      pieChartData: [0, 0, 0]
    }

    let received = 0; //Tiếp nhận
    let resolved = 0; //Giải quyết
    let early = 0; //Sớm hạn
    let onTime = 0;//Đúng hạn
    let overdue = 0;//Trễ hạn

    early = this.sum('resolvedEarly');
    overdue = this.sum('resolvedOverdue');
    onTime = this.sum('resolvedOnTime');
    received = this.sum('receivedModify');
    resolved = this.sum('resolved');

    let earlyRate = ((early * 100) / resolved).toFixed(2); //Tỉ lệ Sớm hạn      
    let onTimeRate = this.sum('onTimeRate'); // Tỉ lệ đúng hạn
    let overdueRate = this.sum('lateRate');  //Tỉ lệ Trễ hạn    

    pieData.pieChartData[0] = early;
    pieData.pieChartData[1] = onTime;
    pieData.pieChartData[2] = overdue;
    pieData.pieChartLabels[0] = 'Sớm hạn: ' + early + '/' + resolved + ', Tỷ lệ: ' + earlyRate + '%';
    pieData.pieChartLabels[1] = 'Đúng hạn: ' + onTime + '/' + resolved + ', Tỷ lệ: ' + onTimeRate + '%';
    pieData.pieChartLabels[2] = 'Trễ hạn: ' + overdue + '/' + resolved + ', Tỷ lệ: ' + overdueRate + '%';

    this.pieListData.push(pieData);
    this.pieListData.sort((a, b) => a.indexArr - b.indexArr);
  }

  fetchDataPieCharProcessing() {
    this.pieListDataProcessing = [];
    const pieData = {
      nameLevelAgency: '',
      indexArr: 0,
      pieChartLabels: ['Trong hạn', 'Quán hạn'],
      pieChartData: [0, 0, 0]
    }

    let unresolved = 0; //đang giải quyết
    let unresolvedOnTime = 0; //trong hạn
    let unresolvedOverdue = 0; //quá hạn

    unresolved = this.sum('unresolved');
    unresolvedOnTime = this.sum('unresolvedOnTime');
    unresolvedOverdue = this.sum('unresolvedOverdue');

    // let unresolvedRate = ((unresolvedOnTime * 100) / unresolved).toFixed(2); //Tỉ lệ trong hạn      
    // let unresolvedOverdueRate = ((unresolvedOverdue * 100) / unresolved).toFixed(2); // Tỉ lệ quá hạn    

    let unresolvedRate = this.sum('unresolvedRate'); //Tỉ lệ trong hạn      
    let unresolvedOverdueRate = this.sum('unresolvedOverdueRate'); // Tỉ lệ quá hạn  

    pieData.pieChartData[0] = unresolvedOnTime;
    pieData.pieChartData[1] = unresolvedOverdue;
    pieData.pieChartLabels[0] = 'Trong hạn: ' + unresolvedOnTime + '/' + unresolved + ', Tỷ lệ: ' + unresolvedRate + '%';
    pieData.pieChartLabels[1] = 'Quá hạn: ' + unresolvedOverdue + '/' + unresolved + ', Tỷ lệ: ' + unresolvedOverdueRate + '%';

    this.pieListDataProcessing.push(pieData);
    this.pieListDataProcessing.sort((a, b) => a.indexArr - b.indexArr);
  }

  async searchBtn() {
    const formObj = this.searchForm.getRawValue();

    if (formObj.toDate === '' || formObj.fromDate === '') {
      const message = {
        vi: 'Ngày thống kê không được để trống!',
        en: 'Statistical date cannot be blank!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    } else if (new Date(formObj.toDate) < new Date(formObj.fromDate)) {
      const message = {
        vi: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
        en: 'Start date must be less than or equal to end date!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    }

    this.isLoading = true;
    this.isAgencyTreeOpen = false;
    let listAllSector = [];
    let listAllProcedure = [];
    let listAgencys = [];
    let listSubAgencys: any = [];
    let isDvc = this.enableModifiedGeneralReportQni;
    if(this.reportTypeId !== "0"){
      isDvc = false;
    }
    console.log('savedItems', this.savedItems)
    let listAgencyReport = [];
    if (!this.xpandStatus) {
      listAgencyReport = [...this.savedItems]
    } else {
      const formObj = this.searchForm.getRawValue();
      let agencys = formObj.subAgencyCtrl;
      if (agencys.length != 0) {
        listAgencyReport = [...agencys];
      }
      else if (this.listSubAgency.length != 0)
        listAgencyReport = [...this.listSubAgency]
    }
    if (listAgencyReport.length == 0) {
      const idTemp = await this.qniStatisticsService.getRootAgencies(`?id=${this.deploymentService.env?.OS_QNI?.agency?.UBND_Tinh_QNI}`).toPromise();
      if (idTemp != null && idTemp.length > 0)
        listAgencys = [idTemp[0]?.id];
      else
        listAgencys = [this.deploymentService.env?.OS_QNI?.agency?.UBND_Tinh_QNI]
      let search = "?parent-id=" + listAgencys
      listSubAgencys = await this.basedataService.getSubAgencyByParentId(search).toPromise();
      if (listSubAgencys != null && listSubAgencys.length > 0)
        listAgencys = [...listSubAgencys.map(x => x.id)];
    } else
      listAgencys = [...listAgencyReport];
    if (this.reportTypeId === '1' && this.sectorData.selected.length == 0) {
      listAllSector = []
    } else
      listAllSector = [...this.sectorData.selected]

    if (this.reportTypeId === '2' && this.procedureData.selected.length == 0) {
      listAllProcedure = []
    }
    else
      listAllProcedure = [...this.procedureData.selected]

    console.log('listAgencys', listAgencys)
    listAgencys = listAgencys.filter(function (element) {
      return element !== undefined;
    });
    let agencysModify = formObj.subAgencyCtrl.filter(elements => {
      return (elements != null && elements !== undefined && elements !== "");
    });
    this.listAgencySearch = [...agencysModify];
    listAgencys = [...agencysModify];
    const searchString: string = '?agency-id=' + listAgencys.join(',') + '&report-type-id=' + this.reportTypeId
    + '&from-date=' + this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z'
    + '&to-date=' + this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z'
    + '&sector-id=' + listAllSector.join(',')
    + '&procedure-id=' + listAllProcedure.join(',') + '&is-dvc=' + isDvc;
    this.ELEMENTDATA = [];
    let data: any = [];

    if (this.enableChangeReporter) {
       data = await this.qniStatisticsService.getDossierStatisticGeneralFromReporter(searchString).toPromise();
    }else{
      data = await this.qniStatisticsService.getDossierStatisticGeneral(searchString).toPromise();
    }

    if (data) {
      let sectorId;
      let sectorName;
      let stt = 1;
      for (let i = 0; i < data.length; i++) {
        switch (this.reportTypeId) {
          case '1': {
            sectorId = data[i]?.sector?.id;
            sectorName = data[i]?.sector?.name;
            break;
          }
          case '2': {
            sectorId = data[i]?.procedure?.id;
            sectorName = data[i]?.procedure?.name;
            break;
          }
          default: {
            sectorId = data[i]?.agency?.id;
            sectorName = data[i]?.agency?.name;
            break;
          }
        }
        let obj = {
          stt: stt,
          sectorId,
          sectorName,
          agencyId: data[i]?.agency?.id,
          received: data[i].received + data[i].receivedOld,
          receivedOnline: data[i].receivedOnline,
          receivedDirect: data[i].receivedDirect,
          receivedOld: data[i].receivedOld,
          resolved: data[i].resolved,
          resolvedEarly: data[i].resolvedEarly,
          resolvedOnTime: data[i].resolvedOnTime,
          resolvedOverdue: data[i].resolvedOverdue,
          unresolved: data[i].unresolved,
          unresolvedOnTime: data[i].unresolvedOnTime,
          unresolvedOverdue: data[i].unresolvedOverdue,
          direct: data[i].receivedDirect - data[i].receivedPostal - data[i].receivedPublicPostal,
          receivedPostal: data[i].receivedPostal,
          receivedPublicPostal: data[i].receivedPublicPostal,
          receivedSmartphone: data[i].receivedSmartphone,
          withdraw: data[i].withdraw,
          onTimeRate: 0, // tỷ lệ đúng hạn
          lateRate: data[i].resolved > 0 ? Math.round(((data[i].resolvedOverdue / data[i].resolved) * 100) * 10) / 10 : 0, // tỷ lệ trễ hạn
          receivedModify: data[i].receivedOnline + data[i].receivedDirect, // tổng tiếp nhận
          resolvedOnTimeModify: data[i].resolvedEarly + data[i].resolvedOnTime,
          unresolvedRate: data[i].unresolved > 0 ? Math.round(((data[i].unresolvedOnTime / data[i].unresolved) * 100) * 10) / 10 : 0,
          unresolvedOverdueRate: data[i].unresolved > 0 ? Math.round(((data[i].unresolvedOverdue / data[i].unresolved) * 100) * 10) / 10 : 0,
          procedureUsed: data[i].procedureUsed ? data[i].procedureUsed : 0
        }

        obj.onTimeRate = data[i].resolved > 0 ? Math.round((parseInt(obj.resolvedOnTimeModify) / parseInt(obj.resolved) * 100) * 10) / 10 : 0
        if (!this.checkZeroData(obj)) {
          this.ELEMENTDATA.push(obj);
          stt++;
        }

      }
    }

    this.dataSource.data = this.ELEMENTDATA;
    this.isLoading = false;

    // mapping chart
    this.timeTitleFormatChart = `Từ ngày: ${this.datePipe.transform(formObj.fromDate, 'dd-MM-yyyy')} đến ngày: ${this.datePipe.transform(formObj.toDate, 'dd-MM-yyyy')}`.toUpperCase();
    this.titleLabelChartResolved = `Thống kê hồ sơ đã xử lý ${this.listLevelReport.filter(x => x.id == formObj.levelReportCtrl)[0].name}`.toUpperCase();
    this.fetchDataPieChar();// Get thống kê hồ sơ đang giải quyết
    this.titleLabelChartProcessing = `Thống kê hồ sơ đang xử lý ${this.listLevelReport.filter(x => x.id == formObj.levelReportCtrl)[0].name}`.toUpperCase();
    this.fetchDataPieCharProcessing();// Get thống kê hồ sơ đang giải quyết
    this.titleLabelChartBarResolved = `Thống kê hồ sơ đã xử lý ${this.listLevelReport.filter(x => x.id == formObj.levelReportCtrl)[0].name}`.toUpperCase();
    this.getDataChartBar();
    this.titleLabelChartBarProcessing = `Thống kê hồ sơ đang xử lý ${this.listLevelReport.filter(x => x.id == formObj.levelReportCtrl)[0].name}`.toUpperCase();
    this.getDataChartBarProcessing();
  }

  async onControlSearch(type, event) {
    setTimeout(async () => {
      const keyword = event?.target?.value;
      switch (type) {
        case 'sector': {
          await this.onControlReset('sector', false);
          if (event) {
            event.preventDefault();
            this.sectorData.keyword = keyword;
          }
          this.sectorData.list = this.sectorData.backup.filter(e => e.name.includes(keyword) || e.id.includes(keyword));
          break;
        }
        case 'procedure': {
          await this.onControlReset('procedure', false);
          if (event) {
            event.preventDefault();
            this.procedureData.keyword = event.target.value;
          }
          this.procedureData.list = this.procedureData.backup.filter(e => e.name.includes(keyword) || e.id.includes(keyword));
          break;
        }
      }
    }, 300);
  }

  getListSector(pageState) {
    let idTemp = this.getListAgencyReport();
    this.qniStatisticsService.getRootAgencies(`?id=${idTemp.join(',')}`).subscribe(rootAgencies => {
      const { keyword, page, pageSize } = pageState;
      if (rootAgencies && rootAgencies.length) {
        const searchString = `?arr-agency-id=${rootAgencies.map(e => e.id).join(',')}`;
        this.qniStatisticsService.getListSectorsByAgencyQNI(searchString).subscribe(data => {
          this.sectorData.last = true;//data.last;
          this.sectorData.totalElement = data.length//data.totalPages;
          if (page === 0) {
            this.sectorData.list = data;
          } else {
            this.sectorData.list = this.sectorData.list.concat(data);
          }
          this.sectorData.backup = [...this.sectorData.list];
        }, err => {
          console.log(err);
        });
      }
    }, err => {
      console.log(err);
    });
  }
  getHeaderRowDef(): string[] {
    const baseArray = ['No1', 'No2', 'No3', 'No4', 'No5', 'No6'];
    if (this.enableModifiedGeneralReportQni) {
      return [...baseArray, 'No7'];
    }
    return baseArray;
  }

  getHeaderRowDefThird(): string[] {
    const baseArray = ['Third1', 'Third2', 'Third3', 'Third4', 'Third5', 'Third6', 'Third7', 'Third8', 'Third9', 'Third10', 'Third11', 'Third12', 'Third13', 'Third14', 'Third15', 'Third16'];
    if (this.enableModifiedGeneralReportQni) {
      return [...baseArray, 'Third17'];
    }
    return baseArray;
  }

  getListProcedure(pageState) {
    let idTemp = this.getListAgencyReport();
    this.qniStatisticsService.getRootAgencies(`?id=${idTemp.join(',')}`).subscribe(rootAgencies => {
      const { keyword, page, pageSize } = pageState;
      if (rootAgencies && rootAgencies.length) {
        const searchString = `?agency-id=${rootAgencies.map(e => e.id).join(',')}`;
        this.qniStatisticsService.getListProcedureByAgency(searchString).subscribe(data => {
          console.log('data', data)
          this.procedureData.last = true//data.last;
          this.procedureData.totalElement = data.numberOfElements;
          if (page === 0) {
            this.procedureData.list = data.content;

          } else {
            this.procedureData.list = this.procedureData.list.concat(data.content);
          }
          this.procedureData.backup = [...this.procedureData.list];
        }, err => {
          console.log(err);
        });
      }
    });
  }

  agencyTreeChange(obj) {
    if (Array.isArray(obj)) {
      this.savedItems = obj.map(item => item.id);
      switch (this.reportTypeId) {
        case '1': {
          this.onControlReset('sector');
          break;
        }
        case '2': {
          this.onControlReset('procedure');
          break;
        }
      }
    }
  }

  onReportTypeChange(event) {
    switch (event?.value) {
      case '0': {
        this.excelColumnName = 'Đơn vị';
        this.excelFileName = 'Thong ke ho so theo don vi';
        break;
      }
      case '2': {
        this.onControlReset('procedure');
        this.sectorData.selected = [];
        this.excelColumnName = 'Thủ tục hành chính';
        this.excelFileName = 'Thong ke ho so theo thu tuc ';
        break;
      }
      default: {
        this.onControlReset('sector');
        this.procedureData.selected = [];
        this.excelColumnName = 'Lĩnh vực giải quyết';
        this.excelFileName = 'Thong ke ho so theo linh vuc ';
        break;
      }
    }
  }

  onTreeOpenChange(value) {
    this.isAgencyTreeOpen = value;
  }

  async onControlNextBatch(type) {
    clearTimeout(this.timeOutSearch);
    this.timeOutSearch = setTimeout(async () => {
      switch (type) {
        case 'sector': {
          if (!this.sectorData.last) {
            this.sectorData.page++;
            await this.getListSector(this.sectorData);
          }
          break;
        }
        case 'procedure': {
          if (!this.procedureData.last) {
            this.procedureData.page++;
            await this.getListProcedure(this.procedureData);
            await this.getListProcedureAll();
          }
          break;
        }
      }
    }, this.msTimeOutSearch);
  }

  async onControlReset(type, force = true) {
    const initPage = {
      page: 0,
      list: [],
      keyword: '',
      totalElement: 0,
    };
    switch (type) {
      case 'sector': {
        this.sectorData = {
          ...this.sectorData,
          ...initPage,
        };
        if (force) {
          await this.getListSector(this.sectorData);
        }
        break;
      }
      case 'procedure': {
        this.procedureData = {
          ...this.procedureData,
          ...initPage,
        };
        if (force) {
          await this.getListProcedure(this.procedureData);
          await this.getListProcedureAll();
        }
        break;
      }
    }
  }

  getListProcedureAll() {
    let idTemp = this.getListAgencyReport();
    this.qniStatisticsService.getRootAgencies(`?id=${idTemp.join(',')}`).subscribe(rootAgencies => {
      if (rootAgencies && rootAgencies.length) {
        const searchString = `?agency-id=${rootAgencies.map(e => e.id).join(',')}`;
        this.qniStatisticsService.getListProcedureByAgencyQNI(searchString).subscribe(data => {
          console.log('data', data)
          this.listAllProcedure = data.content;
        }, err => {
          console.log(err);
        });
      }
    });
  }

  //SRTART IGATESUPP-42249
  onClickOpenAdvancedSearchBox() {
    this.xpandStatus = !this.xpandStatus;
  }
  async optionClickLevelReport() {
    let newStatus = true;
    this.levelReportMatSelect.options.forEach((item: MatOption) => {
      if (!item.selected) {
        newStatus = false;
      }
      if (item.value === '') {
        item.deselect();
      }
    });
    this.allSelectedlevelReport = newStatus;
    await this.getListSubAgency(false);
    const allSelectedValues = this.listSubAgencyFilteredSearch.map(item => item.id);
    this.searchForm.get("subAgencyCtrl").setValue(allSelectedValues);
  }

  toggleAllSelectionLevelReport() {
    this.allSelectedlevelReport = !this.allSelectedlevelReport;  // to control select-unselect
    if (this.allSelectedlevelReport) {
      this.levelReportMatSelect.options.forEach((item: MatOption) => {
        if (!item.selected) {
          item.select();
        }
        if (item.value === '') {
          item.deselect();
        }
      });
    } else {
      this.levelReportMatSelect.options.forEach((item: MatOption) => item.deselect());
    }
  }

  async getListSubAgency(isSearch) {
    if (isSearch) {
      const search = this.searchSubAgencyCtrl.value;
      this.listSubAgencyFilteredSearch = this.listSubAgency.filter(x => x.name.toLowerCase().includes(search.toLowerCase()));
    } else {
      const formObj = this.searchForm.getRawValue();
      let searchString = "?ancestor-id=" + this.deploymentService.env?.OS_QNI?.agency?.UBND_Tinh_QNI;
      let tag_id = "&tag-id=";
      let levelTagID = this.deploymentService.env.OS_QNI.levelTagID;
      switch (formObj.levelReportCtrl) {
        case '1': tag_id += levelTagID.capSo + ","; break;
        case '2': tag_id += levelTagID.capHuyen + ","; break;
        case '3': tag_id += levelTagID.capXa + ","; break;
        case '4': tag_id += levelTagID.daiDai + ","; break;

        default: tag_id += levelTagID.capSo + ","; break;
      }
      tag_id = tag_id.substring(0, tag_id.length - 1);
      searchString += tag_id;
      // subscribe la callback function 
      // thay the bang toPromise de no doi xong roi moi co cai list
      const data: any = await this.qniStatisticsService.getListAgencyByTagId(searchString).toPromise();
      this.listSubAgency = [...data]
      this.listSubAgencyFilteredSearch = [...this.listSubAgency]
    }
  }

  toggleAllSelection() {
    this.allSelectedSubAgency = !this.allSelectedSubAgency;  // to control select-unselect
    if (this.allSelectedSubAgency) {
      this.subAgencyMatSelect.options.forEach((item: MatOption) => {
        if (!item.selected) {
          item.select();
        }
        if (item.value === '') {
          item.deselect();
        }
      });
    } else {
      this.subAgencyMatSelect.options.forEach((item: MatOption) => item.deselect());
    }
  }

  optionClick() {
    let newStatus = true;
    this.subAgencyMatSelect.options.forEach((item: MatOption) => {
      if (!item.selected) {
        newStatus = false;
      }
      if (item.value === '') {
        item.deselect();
      }
    });
    this.allSelectedSubAgency = newStatus;
  }

  getListAgencyReport() {
    let idTemp = [];
    if (!this.xpandStatus) {
      if (this.savedItems.length == 0) {
        idTemp = [this.deploymentService.env?.OS_QNI?.agency?.UBND_Tinh_QNI];
      }
      else
        idTemp = [...this.savedItems]
    } else {
      const formObj = this.searchForm.getRawValue();
      let agencys = formObj.subAgencyCtrl;
      if (agencys.length != 0) {
        idTemp = agencys;
      }
      else if (this.listSubAgency.length != 0)
        idTemp = [...this.listSubAgency]
      else
        idTemp = [this.deploymentService.env?.OS_QNI?.agency?.UBND_Tinh_QNI];
    }
    idTemp = idTemp.filter(function (element) {
      return element !== undefined;
    });
    return idTemp;
  }

  //END IGATESUPP-42249
  @ViewChild('statisticAgencyTree') statisticAgencyTree: AgencyTreeComponent;
  @HostListener('document:click', ['$event.path'])
  public onGlobalClick(targetElementPath: Array<any>) {
    if (!!targetElementPath) {
      const elementRefInPath = targetElementPath.find(e => e === this.statisticAgencyTree);
      if (!elementRefInPath) {
        // this.isAgencyTreeOpen = false;
      }
    }
  }

  formatNumber(n: number) {
    return (n == null || false) ? '' : n.toString().replace(/(.)(?=(\d{3})+$)/g, '$1.');
  }

  formatRateNumber(n: number) {
    return (n == null || false) ? '' : n.toString().replace(/(.)(?=(\d{3})+$)/g, '$1.').replace('.',',');
  }

  sum(key: keyof any) {
    const calculateRate = (key: string) => {
      let nonZeroValues = this.dataSource.data.filter(item => Number(item[key]) !== 0);
      let temp = nonZeroValues.reduce((a, b) => a + (Number(b[key]) || 0), 0);
      return nonZeroValues.length > 0 ? (Math.round((temp / nonZeroValues.length) * 10) / 10) : 0;
    };
    const calculateRemainingRate = (rateKey: string) => {
        return Math.round((100 - this.sum(rateKey)) * 10) / 10;
    };
    switch (key) {
        case "onTimeRate":
            return calculateRate('onTimeRate');
        case "lateRate":
            return calculateRemainingRate("onTimeRate");
        case "unresolvedRate":
            return calculateRate('unresolvedRate');
        case "unresolvedOverdueRate":
            return calculateRemainingRate("unresolvedRate");
        default:
            return this.dataSource.data.reduce((a, b) => a + (Number(b[key]) || 0), 0);
    }
  }

  checkZeroData(obj: any) {
    try {
      if (obj.received == 0 &&
        obj.receivedOnline == 0 &&
        obj.receivedDirect == 0 &&
        obj.receivedOld == 0 &&
        obj.resolved == 0 &&
        obj.resolvedEarly == 0 &&
        obj.resolvedOnTime == 0 &&
        obj.resolvedOverdue == 0 &&
        obj.unresolved == 0 &&
        obj.unresolvedOnTime == 0 &&
        obj.unresolvedOverdue == 0 &&
        obj.direct == 0 &&
        obj.receivedPostal == 0 &&
        obj.receivedPublicPostal == 0 &&
        obj.receivedSmartphone == 0 && //fix bug
        obj.withdraw == 0)
        return true
      return false;
    }
    catch (e) {
      return false;
    }
  }
}
