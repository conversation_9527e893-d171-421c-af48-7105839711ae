import { Injectable } from '@angular/core';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class ExportExcelService {
  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  constructor() { }

  public exportAsExcelFile(
    reportHeading: string,
    reportSubHeading: string,
    headersArray: any[],
    subheaderArray: any[],
    json: any[],
    footerData: any,
    excelFileName: string,
    sheetName: string,
    agencyName: string,
    subAgencyName: string,
    bannerName: string,
    subBannerName: string
  ) {
    const header = headersArray;
    const subheader = subheaderArray;
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:C1');
    worksheet.getCell('A1').value = agencyName;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };


    worksheet.mergeCells('H1:N1');
    worksheet.getCell('H1').value = bannerName;
    worksheet.getCell('H1').alignment = { horizontal: 'center', vertical: 'middle' };


    worksheet.mergeCells('H2:N2');
    worksheet.getCell('H2').value = subBannerName;
    worksheet.getCell('H2').alignment = { horizontal: 'center', vertical: 'middle' };


    worksheet.addRow([]);
    worksheet.mergeCells('A2:C2');
    worksheet.getCell('A2').value = subAgencyName;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A2').font = { bold: true };

    worksheet.addRow([]);

    worksheet.addRow([]);
    worksheet.mergeCells('A4:' + this.numToAlpha(header.length - 1) + '4');
    worksheet.getCell('A4').value = reportHeading;
    worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A4').font = { size: 15, bold: true };

    if (reportSubHeading !== '') {
      worksheet.addRow([]);
      worksheet.mergeCells('A5:' + this.numToAlpha(header.length - 1) + '5');
      worksheet.getCell('A5').value = reportSubHeading;
      worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle' };
    }
    const headerRow = worksheet.addRow(header);
    const subheaderRow = worksheet.addRow(subheader);
    worksheet.mergeCells('A7:A8');
    worksheet.mergeCells('B7:B8');
    worksheet.mergeCells('C7:C8');
    worksheet.mergeCells('D7:D8');
    worksheet.mergeCells('E7:E8');
    worksheet.mergeCells('F7:F8');

    worksheet.mergeCells('G7:H7');
    worksheet.mergeCells('I7:J7');
    worksheet.mergeCells('K7:L7');

    worksheet.mergeCells('M7:M8');
    worksheet.mergeCells('N7:N8');

    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('K').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('M').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('N').alignment = { horizontal: 'center', vertical: 'middle' };

    worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
    // add header row
    subheaderRow.eachCell((cell, index) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'C0C0C0C0' },
        bgColor: { argb: 'FF0000FF' }
      };
      cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      cell.font = { size: 12, bold: true };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };

      worksheet.getColumn(index).width = header[index - 1].length < 20 ? 20 : header[index - 1].length;
    });

    // Cell style: fill and border
    headerRow.eachCell((cell, index) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'C0C0C0C0' },
        bgColor: { argb: 'FF0000FF' }
      };
      cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      cell.font = { size: 12, bold: true };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };

      worksheet.getColumn(index).width = header[index - 1].length < 20 ? 20 : header[index - 1].length;
    });

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }
    // Add Data and Conditional Formatting
    data.forEach((element: any) => {
      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      if (element.isDeleted === 'Y') {
        const deletedRow = worksheet.addRow(eachRow);
        deletedRow.eachCell((cell) => {
          cell.font = { name: 'Calibri', family: 4, size: 11, bold: false, strike: true };
        });
      } else {
        const borderrow = worksheet.addRow(eachRow);
        borderrow.eachCell((cell) => {
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          if (cell.address.indexOf('C') !== -1) {
            cell.alignment = { wrapText : true, horizontal: 'left', vertical: 'middle' };
          }else{
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
          }
        });
      }
    });
    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        const cellMerge = 'A' + footerRow.number + ':C' + footerRow.number;
        worksheet.mergeCells(cellMerge);
        footerRow.eachCell((cell) => {
          cell.font = { size: 15, bold: true };
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      });
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  public exportAsExcelFileSimple(
    reportHeading: string,
    reportSubHeading: string,
    nameReport: string,
    subNameReport: string,
    headersArray: any[],
    subheaderArray: any[],
    json: any[],
    excelFileName: string,
    sheetName: string,
    agencyName: string
  ) {
    const header = headersArray;
    const subheader = subheaderArray;
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:B1');
    worksheet.getCell('A1').value = nameReport;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };


    worksheet.mergeCells('A2:B2');
    worksheet.getCell('A2').value = subNameReport;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

    if (this.language === 228){
      worksheet.getCell('D1').value = 'Kỳ báo cáo: ';
    }else{
      worksheet.getCell('D1').value = 'Reporting period: ';
    }
    worksheet.getCell('D1').alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };

    if (this.language === 228){
      worksheet.getCell('D2').value = 'Đơn vị báo cáo: ';
    }else{
      worksheet.getCell('D2').value = 'Reporting unit: ';
    }
    worksheet.getCell('D2').alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };

    worksheet.getCell('E2').value = agencyName;
    worksheet.getCell('E2').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

    if (this.language === 228){
      worksheet.getCell('D3').value = 'Đơn vị nhận báo cáo: ';
    }else{
      worksheet.getCell('D3').value = 'Unit receiving report: ';
    }
    worksheet.getCell('D3').alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };

    worksheet.mergeCells('D6:E6');
    if (this.language === 228){
      worksheet.getCell('D6').value = 'Đơn vị tính: Số bộ hồ sơ';
    }else{
      worksheet.getCell('D6').value = 'Unit: Number of dossiers';
    }
    worksheet.mergeCells('A4:E4');
    worksheet.getCell('A4').value = reportHeading;
    worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getRow(4).font = { size: 12, bold: true };

    worksheet.mergeCells('A5:E5');
    worksheet.getCell('A5').value = reportSubHeading;
    worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };


    const headerRow = worksheet.addRow(header);
    const subheaderRow = worksheet.addRow(subheader);
    worksheet.getColumn('B').width = 45;
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

    worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
    // add header row
    subheaderRow.eachCell((cell, index) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'C0C0C0C0' },
        bgColor: { argb: 'FF0000FF' }
      };
      cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      cell.font = { size: 12, bold: true };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };

      worksheet.getColumn(index).width = header[index - 1].length < 20 ? 20 : header[index - 1].length;
    });

    // Cell style: fill and border
    headerRow.eachCell((cell, index) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'C0C0C0C0' },
        bgColor: { argb: 'FF0000FF' }
      };
      cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      cell.font = { size: 12, bold: true };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };

      worksheet.getColumn(index).width = header[index - 1].length < 20 ? 20 : header[index - 1].length;
    });

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }
    // Add Data and Conditional Formatting
    data.forEach((element: any) => {
      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      const p = 'isArray';
      if (element.hasOwnProperty(p)){
        const borderrow = worksheet.addRow(eachRow[0]);
        borderrow.eachCell((cell) => {
          cell.font = { size: 12, bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'ffffff' },
            bgColor: { argb: 'DC143C' }
          };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          worksheet.mergeCells('A' + cell.row + ':D' + cell.row);
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        });
        for (const val of eachRow[2]){
          const r = worksheet.addRow(val);
          r.eachCell((cell) => {
            cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
            cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            // tslint:disable-next-line: max-line-length
            worksheet.getCell('C' + cell.row).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
            // tslint:disable-next-line: max-line-length
            worksheet.getCell('D' + cell.row).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          });
        }
      }
      else{
        const borderrow = worksheet.addRow(eachRow);
        borderrow.eachCell((cell) => {
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      }
    });
    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  public exportAsExcelFileForm6a(
    reportHeading: string,
    reportSubHeading: string,
    nameReport: string,
    subNameReport: string,
    json: any[],
    footerData: any,
    excelFileName: string,
    sheetName: string,
    agencyName: string
  ) {
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:C1');
    worksheet.getCell('A1').value = nameReport;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };


    worksheet.mergeCells('A2:C2');
    worksheet.getCell('A2').value = subNameReport;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };

    worksheet.mergeCells('L1:M1');
    if (this.language === 228){
      worksheet.getCell('L1').value = 'Kỳ báo cáo: ';
    }else{
      worksheet.getCell('L1').value = 'Reporting period: ';
    }
    worksheet.getCell('L1').alignment = { horizontal: 'left', vertical: 'middle' };

    worksheet.mergeCells('L2:M2');
    if (this.language === 228){
      worksheet.getCell('L2').value = 'Đơn vị báo cáo: ';
    }else{
      worksheet.getCell('L2').value = 'Reporting unit: ';
    }
    worksheet.getCell('K2').alignment = { horizontal: 'left', vertical: 'middle' };
    worksheet.mergeCells('N2: O2');
    worksheet.getCell('N2').value = agencyName;
    worksheet.getCell('N2').alignment = { horizontal: 'center', vertical: 'middle' };

    worksheet.mergeCells('L3:M3');
    if (this.language === 228){
      worksheet.getCell('L3').value = 'Đơn vị nhận báo cáo: ';
    }else{
      worksheet.getCell('L3').value = 'Unit receiving report: ';
    }
    worksheet.getCell('L3').alignment = { horizontal: 'left', vertical: 'middle' };

    if (this.language === 228){
      worksheet.getCell('L6').value = 'Đơn vị tính: Số bộ hồ sơ';
    }else{
      worksheet.getCell('L6').value = 'Unit: Number of dossiers';
    }
    worksheet.mergeCells('A4:P4');
    worksheet.getCell('A4').value = reportHeading;
    worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(4).font = { size: 12, bold: true };

    worksheet.mergeCells('A5:P5');
    worksheet.getCell('A5').value = reportSubHeading;
    worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle' };

    // NỘI DUNG TABLE-HEADER
    worksheet.mergeCells('A7:A9');
    worksheet.getCell('A7').value = 'STT';
    worksheet.mergeCells('B7:B9');
    worksheet.getCell('B7').value = 'Lĩnh vực';
    worksheet.mergeCells('C7:F7');
    worksheet.getCell('C7').value = 'Số hồ sơ nhận giải quyết';
    worksheet.mergeCells('G7:L7');
    worksheet.getCell('G7').value = 'Kết quả giải quyết';
    worksheet.mergeCells('M7:P7');
    worksheet.getCell('M7').value = 'Số hồ sơ giải quyết theo cơ chế một cửa';
    worksheet.mergeCells('C8:C9');
    worksheet.getCell('C8').value = 'Tổng số';
    worksheet.mergeCells('D8:F8');
    worksheet.getCell('D8').value = 'Trong đó';
    worksheet.mergeCells('G8:I8');
    worksheet.getCell('G8').value = 'Số hồ sơ đã giải quyết';
    worksheet.mergeCells('J8:L8');
    worksheet.getCell('J8').value = 'Số hồ sơ đang giải quyết';
    worksheet.mergeCells('M8:M9');
    worksheet.getCell('M8').value = 'Tổng số';
    worksheet.mergeCells('N8:O8');
    worksheet.getCell('N8').value = 'Đã giải quyết';
    worksheet.mergeCells('P8:P9');
    worksheet.getCell('P8').value = 'Đang giải quyết';
    worksheet.getCell('D9').value = 'Số mới tiếp nhận trực tuyến';
    worksheet.getCell('E9').value = 'Số kỳ trước chuyển qua';
    worksheet.getCell('F9').value = 'Số mới tiếp nhận (trực tiếp hoặc dịch vụ bưu chính)';
    worksheet.getCell('G9').value = 'Tổng số';
    worksheet.getCell('H9').value = 'Trả đúng thời hạn';
    worksheet.getCell('I9').value = 'Trả quá hạn';
    worksheet.getCell('J9').value = 'Tổng số';
    worksheet.getCell('K9').value = 'Chưa đến hạn';
    worksheet.getCell('L9').value = 'Quá hạn';
    worksheet.getCell('N9').value = 'Đúng thời hạn';
    worksheet.getCell('O9').value = 'Quá hạn';
    worksheet.getCell('A10').value = '(1)';
    worksheet.getCell('B10').value = '(2)';
    worksheet.getCell('C10').value = '(3)';
    worksheet.getCell('D10').value = '(4)';
    worksheet.getCell('E10').value = '(5)';
    worksheet.getCell('F10').value = '(6)';
    worksheet.getCell('G10').value = '(7)';
    worksheet.getCell('H10').value = '(8)';
    worksheet.getCell('I10').value = '(9)';
    worksheet.getCell('J10').value = '(10)';
    worksheet.getCell('K10').value = '(11)';
    worksheet.getCell('L10').value = '(12)';
    worksheet.getCell('M10').value = '(13)';
    worksheet.getCell('N10').value = '(14)';
    worksheet.getCell('O10').value = '(15)';
    worksheet.getCell('P10').value = '(16)';

    worksheet.getColumn('B').width = 45;
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('K').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('M').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('N').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('O').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('P').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

    worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
    let i = 7;
    const j = 10;
    for (i; i <= j; i++){
      let k = 1;
      const l = 16;
      for (k; k <= l; k++){
        worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true  };
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'C0C0C0C0' },
          bgColor: { argb: 'FF0000FF' }
        };
        worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        worksheet.findCell(i, k).font = { size: 11, bold: true };
      }
    }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }
    // Add Data and Conditional Formatting
    data.forEach((element: any) => {
      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      const p = 'isArray';
      if (element.hasOwnProperty(p)){
        if (element.isArray === 1){
          const borderrow = worksheet.addRow(eachRow[0]);
          borderrow.eachCell((cell) => {
            cell.font = { size: 12, bold: true };
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'ffffff' },
              bgColor: { argb: 'DC143C' }
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            worksheet.mergeCells('A' + cell.row + ':P' + cell.row);
            cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          });
          for (const val of eachRow[2]){
            const r = worksheet.addRow(val);
            r.eachCell((cell) => {
              cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
              cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
              // tslint:disable-next-line: max-line-length
              worksheet.getCell('C' + cell.row).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
              // tslint:disable-next-line: max-line-length
              worksheet.getCell('D' + cell.row).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
            });
          }
        }
        else{
          // form 6đ
          const borderrow = worksheet.addRow(eachRow[0]);
          borderrow.eachCell((cell) => {
            cell.font = { size: 12, bold: true };
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'ffffff' },
              bgColor: { argb: 'DC143C' }
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + cell.row + ':P' + cell.row);
            cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          });
          for (const val of eachRow[2]){
            const r = worksheet.addRow(val[0]);
            r.eachCell((cell) => {
              if (cell.address.indexOf('B') !== -1) {
                worksheet.mergeCells('B' + cell.row + ':P' + cell.row);
              }
              cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
              cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
            });
            for (const v of val[1]){
              const r1 = worksheet.addRow(v);
              r1.eachCell((cell) => {
                cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
              });
            }
          }
        }
      }
      else{
        const borderrow = worksheet.addRow(eachRow);
        borderrow.eachCell((cell) => {
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        });
      }
    });
    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
        worksheet.mergeCells(cellMerge);
        footerRow.eachCell((cell) => {
          cell.font = { size: 15, bold: true };
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true  };
        });
      });
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  private numToAlpha(num: number) {
    let alpha = '';
    for (; num >= 0; num = parseInt((num / 26).toString(), 10) - 1) {
      alpha = String.fromCharCode(num % 26 + 0x41) + alpha;
    }

    return alpha;
  }
}
