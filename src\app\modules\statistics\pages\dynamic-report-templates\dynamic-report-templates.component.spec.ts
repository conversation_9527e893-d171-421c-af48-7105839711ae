import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DynamicReportTemplatesComponent } from './dynamic-report-templates.component';

describe('DynamicReportTemplatesComponent', () => {
  let component: DynamicReportTemplatesComponent;
  let fixture: ComponentFixture<DynamicReportTemplatesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DynamicReportTemplatesComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DynamicReportTemplatesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
