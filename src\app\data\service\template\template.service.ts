import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams, HttpEvent } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TemplateService {
  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private reporter = this.apiProviderService.getUrl('digo', 'reporter');
  private basecat = this.apiProviderService.getUrl('digo', 'basecat');
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');

  getListTemplate(searchString, sort): Observable<any> {
    return this.http.get(this.reporter + '/template/' + searchString + '&sort=' + sort);
  }

  getDetailTemplate(id): Observable<any> {
    return this.http.get(this.reporter + '/template/' + id);
  }

  deleteTemplate(id): Observable<any> {
    return this.http.delete(this.reporter + '/template/' + id);
  }

  updateTemplate(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.reporter + '/template/' + id, data, { headers });
  }

  postTemplate(data): Observable<any> {
    return this.http.post<any>(this.reporter + '/template/' , data);
  }

  getListCategory(searchString): Observable<any> {
    return this.http.get(this.basecat + '/tag/' + searchString);
  }

  getDetailCategory(id): Observable<any> {
    return this.http.get(this.basecat + '/tag/' + id);
  }

  downloadFile(id): Observable<any> {
    return this.http.get(this.reporter + '/template/' + id + '/--file', { responseType: 'blob' as 'json' }).pipe();
  }

  fileUpload(formData): Observable<any> {
    return this.http.post(this.reporter + '/file/', formData);
  }

  deleteFile(path): Observable<any> {
    return this.http.delete(this.reporter + '/file/' + path);
  }

  getDetailTemplatePromise(id): Promise<any> {
    return this.http.get(this.reporter + '/template/' + id).toPromise();
  }

  getTotalPagesProcedureConfig(id): Promise<any> {
    return this.http.get(this.basepad + '/procedure-config/template/--list?spec=page&size=50&template-id=' + id).toPromise();
  }

  getListProcedureConfig(id, searchString): Observable<any> {
    return this.http.get(this.basepad + '/procedure-config/template/--list?spec=page&size=50' + searchString + '&template-id=' + id);
  }

  updateProcedureConfig(id, data): Observable<any> {
    return this.http.put<any>(this.basepad + '/procedure-config/' + id, data);
  }

  getTotalPagesAgencyConfig(id): Promise<any> {
    return this.http.get(this.basepad + '/agency-config/template/--list?spec=page&size=50&template-id=' + id).toPromise();
  }

  getListAgencyConfig(id, searchString): Observable<any> {
    return this.http.get(this.basepad + '/agency-config/template/--list?spec=page&size=50' + searchString + '&template-id=' + id);
  }

  updateAgencyConfig(id, data): Observable<any> {
    return this.http.put<any>(this.basepad + '/agency-config/' + id, data);
  }

  getListDefaultConfig(): Observable<any> {
    return this.http.get(this.basepad + '/default-config/?spec=page');
  }

  updateDefaultConfig(id, data): Observable<any> {
    return this.http.put<any>(this.basepad + '/default-config/' + id, data);
  }

}
