import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { TemplateService } from 'src/app/data/service/template/template.service';
import { JsonEditorComponent, JsonEditorOptions } from 'ang-jsoneditor';
import { DeploymentService } from 'src/app/data/service/deployment.service';

@Component({
  selector: 'app-add-template',
  templateUrl: './add-template.component.html',
  styleUrls: ['./add-template.component.scss']
})

export class AddTemplateComponent implements OnInit {

  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  listAcceptExt = this.env?.acceptFileExtensionTemplate || this.config.acceptFileExtensionTemplate;
  blankVal = '';
  result = [];

  addForm = new FormGroup({
    name: new FormControl(''),
    subsystem: new FormControl(''),
    templateType: new FormControl(''),
    listVariable: new FormControl(''),
    signEnable: new FormControl(''),
    code: new FormControl(''),
  });
  // tslint:disable-next-line: max-line-length
  name = new FormControl('', [Validators.required, Validators.pattern(this.config.regValidators), Validators.minLength(5), Validators.maxLength(255)]);
  subsystem = new FormControl('', [Validators.required, Validators.pattern(this.config.regValidators)]);
  templateType = new FormControl('', [Validators.required, Validators.pattern(this.config.regValidators)]);

  emailFormControl = new FormControl('', [
    Validators.required,
    Validators.email,
  ]);

  listSubsystem = [];

  // Search TemplateType
  keywordTemplateType = '';
  totalPagesTemplateType = 0;
  currentPageTemplateType = 0;
  pageSizeTemplateType = 10;
  timeOutTemplateType: any = null;
  listTemplateType: Array<any> = [];

  detailTemplateType: any = {};
  file: any = {};
  isActive: boolean;

  @ViewChild(JsonEditorComponent) editor: JsonEditorComponent;
  options = new JsonEditorOptions();
  dataJson = [];
  check = true;

  constructor(
    public dialogRef: MatDialogRef<AddTemplateComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmSearchDialogModel,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private snackbarService: SnackbarService,
    private templateService: TemplateService,
  ) {
    this.options.mode = 'code';
    this.options.modes = ['code', 'view'];
    this.options.mainMenuBar = false;
    this.options.statusBar = true;
    this.options.onChange = () => {
      try {
        // console.log(this.editor.get());
      }
      catch (err) {
        // console.log('JSON is invalid');
      }
    };
  }

  ngOnInit(): void {
    console.log(this.listAcceptExt);
    this.getListSubsystem();
    this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
  }

  // Search Function
  getNextBatch(type) {
    switch (type) {
      case 'templateType': {
        this.currentPageTemplateType += 1;
        this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
        break;
      }
    }
  }

  onEnter(type, event) {
    switch (type) {
      case 'templateType': {
        clearTimeout(this.timeOutTemplateType);
        this.timeOutTemplateType = setTimeout(async () => {
          this.keywordTemplateType = event.target.value;
          this.currentPageTemplateType = 0;
          this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
        }, 300);
        break;
      }
    }
  }

  async resetSearchForm(type) {
    switch (type) {
      case 'templateType': {
        this.currentPageTemplateType = 0;
        this.keywordTemplateType = '';
        this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
        break;
      }
    }
  }

  onDismiss(): void {
    this.dialogRef.close();
  }
  checkCheckBoxvalue(event){
    if (event.checked) {
      this.addForm.get('signEnable').setValue(1);
    }else{
      this.addForm.get('signEnable').setValue(0);
    }
  }
  async save() {
    const addFormObj = this.addForm.getRawValue();
    // tslint:disable-next-line: max-line-length
    if (this.name.errors?.required || this.name.errors?.minlength || this.name.errors?.maxlength || addFormObj.templateType.length === 0 || this.addForm.controls.subsystem.invalid) {
      const msgObj = {
        vi: 'Vui lòng điền đầy đủ thông tin!',
        en: 'Please fill full information!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    } else {
      let check = true;
      try {
        addFormObj.listVariable = JSON.stringify(this.editor.get());
      }
      catch (err) {
        check = false;
      }
      this.check = check;

      if (!check) {
        const msgObj = {
          vi: 'Chuỗi Array không đúng định dạng!',
          en: 'Array string is malformed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        return;
      }else {
        const jsonString = JSON.stringify(this.editor.get());
        const jsonLength = jsonString.length;
        if (jsonString.substring(0, 1) === '[' && jsonString.substring(jsonLength - 1, jsonLength) === ']') {
        }else{
          const msgObj = {
            vi: 'Chuỗi Array không đúng định dạng!',
            en: 'Array string is malformed!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          return;
        }
      }

      if (this.result.length === 0) {
        const msgObj = {
          vi: 'Vui lòng tải lên tệp tin!',
          en: 'Please upload file!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      } else {
        const formObj = this.addForm.getRawValue();
        const dataPost: any = {};
        const detailSubsystems: Array<any> = [];
        await this.getDetailTemplateType(formObj.templateType);
        dataPost.name = this.name.value.trim();
        dataPost.code = formObj.code;
        // dataPost.signEnable = formObj.signEnable;
        dataPost.type = this.detailTemplateType;

        if (formObj.subsystem != null) {
          // tslint:disable-next-line: prefer-for-of
          for await (const subsystem of formObj.subsystem) {
            const detailSubsystem: any = {};
            // tslint:disable-next-line: prefer-for-of
            for (let j = 0; j < this.config.listSubsystem.length; j++) {
              if (subsystem === this.config.listSubsystem[j].id) {
                detailSubsystems.push(this.config.listSubsystem[j]);
              }
            }
          }
        }

        dataPost.listVariableString = JSON.stringify(this.editor.get());

        const formData = new FormData();
        formData.append('file', this.result[0]);

        this.templateService.fileUpload(formData).subscribe(response => {
          dataPost.file = response;
          dataPost.subsystem = detailSubsystems;

          this.templateService.postTemplate(dataPost).subscribe(data => {
            const result = {
              name: dataPost.name,
              status: true
            };
            this.dialogRef.close(result);
          }, err => {
            this.templateService.deleteFile(dataPost.file.path).subscribe();
            const result = {
              name: dataPost.name,
              status: false,
              errStatus: err.status
            };
            this.dialogRef.close(result);
          });
        });
      }
    }
  }

  async getDetailTemplateType(id) {
    const transArr: any = [];
    await new Promise<void>((resolve, reject) => {
      this.templateService.getDetailCategory(id).subscribe(data => {
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < data.trans.length; i++) {
          const nameObj: any = {};
          nameObj.languageId = data.trans[i].languageId;
          nameObj.name = data.trans[i].name;
          transArr.push(nameObj);
        }
        this.detailTemplateType.id = data.id;
        this.detailTemplateType.code = data.code;
        this.detailTemplateType.name = transArr;
        resolve();
      }, err => {
        console.log(err);
      });
    });
  }

  getLanguageId(selectedLang) {
    if (selectedLang === 'vi') {
      return 228;
    } else {
      return 46;
    }
  }

  getListSubsystem() {
    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < this.config.listSubsystem.length; i++) {
      const subsystem = {
        id: this.config.listSubsystem[i].id,
        code: this.config.listSubsystem[i].code,
        name: ''
      };
      // tslint:disable-next-line: prefer-for-of
      for (let j = 0; j < this.config.listSubsystem[i].name.length; j++) {
        if (this.config.listSubsystem[i].name[j].languageId === this.getLanguageId(this.selectedLang)) {
          subsystem.name = this.config.listSubsystem[i].name[j].name;
        }
      }
      this.listSubsystem.push(subsystem);
    }
  }

  getListTemplateType(keyword, page, size) {
    const searchString = '--by-category-id?category-id=5f5b27924e1bd312a6f3ae1e&keyword=' + keyword + '&page=' + page + '&size=' + size + '&spec=page&sort=order';

    this.templateService.getListCategory(searchString).subscribe(res => {
      if (page === 0) {
        this.listTemplateType = res.content;
      } else {
        this.listTemplateType = this.listTemplateType.concat(res.content);
      }
      this.totalPagesTemplateType = res.totalPages;
    }, err => {
      console.log(err);
    });
  }

  onFileSelected(event) {
    if (event.target.files.length > 0) {
      // tslint:disable-next-line: max-line-length
      if ((this.listAcceptExt.filter(type => type.toLowerCase() === ('.' + event.target.files[0].name.split('.').pop().toLowerCase()))).length < 1) {
        const msgObj = {
          vi: 'Không hỗ trợ dạng tệp tin này, vui lòng chọn loại tệp tin khác!',
          en: 'This file type is not supported, please choose another file type!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      } else {
        for (const i of event.target.files) {
          this.result.push(i);
        }
        event.target.value = null;
      }
    }
  }

  removeAttachItem(index: number) {
    this.result.splice(index, 1);
    this.blankVal = '';
  }

  bytesToSize(size) {
    if (isNaN(parseFloat(size)) || !isFinite(size)) { return '?'; }
    let unit = 0;
    while (size >= 1024) {
      size /= 1024;
      unit++;
    }
    return size.toFixed(+ 0) + ' ' + this.config.fileUnits[unit];
  }

}

export class ConfirmSearchDialogModel {
  constructor() {
  }
}
