<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title> Cập nh<PERSON>t gi<PERSON>y tờ</h3>
<form [formGroup]="updateForm" (submit)="onConfirm()" class="addForm">
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
        <mat-form-field appearance="outline" fxFlex='49.5'>
            <mat-label> Tên tắt giấy tờ</mat-label>
            <input matInput formControlName="code" required>
        </mat-form-field>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex='49.5'>
            <mat-label> Tên giấy tờ </mat-label>
            <input matInput formControlName="name" required>
        </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
        <mat-form-field appearance="outline" fxFlex='grow'>
            <mat-label> Trạng thái </mat-label>
            <mat-select formControlName="status" required>
                <mat-option value="0">Đóng</mat-option>
                <mat-option value="1" selected>Mở</mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
        <button mat-flat-button fxFlex='30' class="addBtn" type="submit" [disabled]="updateForm.invalid">
            Lưu lại
        </button>
    </div>
</form>