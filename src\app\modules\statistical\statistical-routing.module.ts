import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from 'src/app/core/guard/auth.guard';
import { AdminLayoutComponent } from 'src/app/layouts/admin/admin-layout/admin-layout.component';
import { AdminLayoutModule } from 'src/app/layouts/admin/admin-layout.module';
import { ListStatisticalComponent } from './list-statistical/list-statistical.component';


const routes: Routes = [
  {
    path: '',
    component: AdminLayoutComponent,
    children: [
      { path: '', component: ListStatisticalComponent },
    ],
    canActivate: [AuthGuard],
    data: [
      { roles: ['ACTIVITI_ADMIN']}
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes), AdminLayoutModule],
  exports: [RouterModule]
})

export class StatisticalRoutingModule { }
