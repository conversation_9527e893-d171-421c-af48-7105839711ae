import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AdminLayoutComponent } from 'src/app/layouts/admin/admin-layout/admin-layout.component';
import { AdminLayoutModule } from 'src/app/layouts/admin/admin-layout.module';
import { FormComponent } from './form.component';
import { AuthGuard } from 'src/app/core/guard/auth.guard';

const routes: Routes = [
  {
    path: '',
    component: AdminLayoutComponent,
    children: [
      { path: '', component: FormComponent }
    ],
    canActivate: [AuthGuard],
    data: [
      { roles: ['ACTIVITI_ADMIN']}
    ]
  },
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    AdminLayoutModule
  ],
  exports: [RouterModule]
})
export class FormRoutingModule { }
