<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title i18n>Thêm mới phiếu động</h3>
<form [formGroup]="addForm" class="addForm edit">
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex='grow'>
            <mat-label i18n>M<PERSON> phiếu động</mat-label>
            <input matInput formControlName="code">
        </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex='grow'>
            <mat-label i18n>Tên phiếu động</mat-label>
            <input matInput [formControl]="name" required
                oninput="if(this.value.trim().length === 0) {this.value = null}">
            <mat-error *ngIf="name.errors.required" class="error_Msg">
                <span i18n>Vui lòng nhập tên phiếu động</span>
                <div class="err">
                    <mat-icon>priority_high</mat-icon>
                </div>
            </mat-error>
            <mat-error *ngIf="name.errors.minlength" class="error_Msg">
                <span i18n>Tên quá ngắn</span>
                <div class="err">
                    <mat-icon>priority_high</mat-icon>
                </div>
            </mat-error>
            <mat-error *ngIf="name.errors.maxlength" class="error_Msg">
                <span i18n>Tên quá dài</span>
                <div class="err">
                    <mat-icon>priority_high</mat-icon>
                </div>
            </mat-error>
        </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex='grow'>
            <mat-label i18n>Loại phiếu động</mat-label>
            <mat-select msInfiniteScroll (infiniteScroll)="getNextBatch('templateType')"
                [complete]="totalPagesTemplateType <= currentPageTemplateType+1" formControlName="templateType" required>
                <!-- Tạm thời tắt tính năng search trong combobox -->
                <!-- <div>
                            <div>
                                <input matInput #searchInputTemplateType (keyup)="onEnter('templateType', $event)"
                                    (keydown)="$event.stopPropagation()" placeholder="Nhập từ khóa"
                                    class="search-nested" />
                                <button mat-icon-button class="clear-search-nested"
                                    *ngIf="searchInputTemplateType.value !== ''"
                                    (click)="searchInputTemplateType.value = ''; resetSearchForm('templateType')">
                                    <mat-icon> close </mat-icon>
                                </button>
                            </div>
                        </div> -->
                <mat-option *ngFor="let item of listTemplateType" value="{{ item.id }}"> {{ item.name }}</mat-option>
            </mat-select>
            <mat-error *ngIf="templateType.invalid" class="error_Msg">
                <span i18n>Vui lòng chọn loại phiếu động</span>
                <div class="err">
                    <mat-icon>priority_high</mat-icon>
                </div>
            </mat-error>
        </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex='grow'>
            <mat-label i18n>Phân hệ</mat-label>
            <mat-select formControlName="subsystem" multiple required>
                <mat-option *ngFor='let subsystem of listSubsystem' value="{{subsystem.id}}">
                    {{ subsystem.name }}</mat-option>
            </mat-select>
            <mat-error *ngIf="subsystem.invalid" class="error_Msg">
                <span i18n>Vui lòng chọn phân hệ</span>
                <div class="err">
                    <mat-icon>priority_high</mat-icon>
                </div>
            </mat-error>
        </mat-form-field>
    </div>
    <div style="height: 100px;">
        <div style="border: 1px solid blue;border-bottom: none;background-color: #EBEBEB;">
            <span i18n>Danh sách biến hỗ trợ</span>
        </div>
        <json-editor [options]="options" [data]="dataJson" style="height: 100px;"></json-editor>
        <mat-error *ngIf="check === false" class="error_Msg_Json">
            <span i18n>Giá trị chưa đúng</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </mat-error>
    </div>
    <br><br><br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between" class="uploadFiles">
        <div class="card" fxFlex='row'>
            <div class="cardTitle">
                <span i18n>File đính kèm</span>
                <div class="cardAction fileAttachDone">
                    <mat-spinner diameter="25"></mat-spinner>
                    <div class="done ">
                        <mat-icon>check_circle_outline</mat-icon>
                        <span i18n>Đã lưu</span>
                    </div>
                </div>
            </div>
            <div class="cardContent">
                <div class="drag_upload_btn">
                    <button mat-button *ngIf="result.length < 1">
                        <mat-icon>cloud_upload</mat-icon>
                        <span i18n>Kéo thả tệp tin hoặc</span><a href=""><span i18n> Tải lên</span></a>
                    </button>
                    <input type="file" (change)="onFileSelected($event)" [accept]="listAcceptExt" value="{{blankVal}}"
                        *ngIf="result.length < 1">
                    <div class="fileUploadPreview">
                        <div class="listUploaded" *ngFor="let attach of result; let i = index">
                            <div class="fileInfo">
                                <div class="fileIcon"
                                    [ngStyle]="{'background-image': 'url(' + config.cloudStaticURL + '/icon/files/512x512/docx.png)'}">
                                </div>
                                <div class="dGrid">
                                    <p class="fileName" matTooltip="" [matTooltipPosition]="'right'">
                                        {{attach.name}}</p>
                                    <p class="fileSize">{{bytesToSize(attach.size)}}</p>
                                </div>
                                <a mat-icon-button class="deleteFile">
                                    <mat-icon (click)="removeAttachItem(i)">
                                        close
                                    </mat-icon>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

    </div>
    <!-- <div fxLayout="row" class="example-margin" style="margin-top: 2.5em;" fxLayoutAlign="start" fxLayout.xs="row"
        fxLayout.sm="row">
        <mat-checkbox (change)="checkCheckBoxvalue($event)">Ký số</mat-checkbox>
    </div> -->
    <div fxLayout="row" fxLayoutAlign="center" fxLayout.xs="row" fxLayout.sm="row">
        <button mat-flat-button fxFlex='grow' class="searchBtn" (click)="save()">
            <span i18n>Lưu lại</span>
        </button>
    </div>
</form>