// ====================================================== Pagination
.frm_Pagination .temp_Arr {
    display: none;
}

::ng-deep .frm_Pagination .pageSize .mat-form-field-appearance-outline .mat-form-field-outline {
    color: #e8e8e8;
}

.frm_Pagination {
    padding: 1em 0;
    display: flex;
    width: 100%;
}

::ng-deep .frm_Pagination .pageSize .mat-form-field-appearance-outline .mat-form-field-wrapper {
    width: 4.5em;
    margin: 0 0.5em;
}

::ng-deep .frm_Pagination .pageSize .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.5em 0;
}

::ng-deep .frm_Pagination .pageSize .mat-form-field-appearance-outline .mat-select-arrow-wrapper {
    padding-top: 0.5em;
}

::ng-deep .frm_Pagination .pageSize .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 0.5em 0.5em 0.5em;
}

.frm_Pagination .control {
    text-align: right;
    margin-left: auto;
}

::ng-deep .frm_Pagination .control .ngx-pagination a {
    outline: unset;
    background-color: #e8e8e8;
    border-radius: 4px;
    margin: 0 .2em;
}

::ng-deep .frm_Pagination .control .ngx-pagination .current {
    background: #ce7a58;
    border-radius: 4px;
    margin: 0 .2em;
}

::ng-deep .frm_Pagination .control .ngx-pagination .pagination-previous a::before,
::ng-deep .frm_Pagination .control .ngx-pagination .pagination-previous.disabled::before {
    font-family: "Material Icons";
    content: "chevron_left";
    vertical-align: middle;
    transform: scale(1.5);
    margin-right: 0;
    color: #1E2F41;
}

::ng-deep .frm_Pagination .control .ngx-pagination .pagination-next a::after,
::ng-deep .frm_Pagination .control .ngx-pagination .pagination-next.disabled::after {
    font-family: "Material Icons";
    content: "chevron_right";
    vertical-align: middle;
    transform: scale(1.5);
    margin-left: 0;
    color: #1E2F41;
}

// ====================================================== END Pagination