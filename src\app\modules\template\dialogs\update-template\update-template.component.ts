import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { TemplateService } from 'src/app/data/service/template/template.service';
import { JsonEditorComponent, JsonEditorOptions } from 'ang-jsoneditor';
import { DeploymentService } from 'src/app/data/service/deployment.service';

@Component({
  selector: 'app-update-template',
  templateUrl: './update-template.component.html',
  styleUrls: ['./update-template.component.scss']
})

export class UpdateTemplateComponent implements OnInit {

  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  listAcceptExt = this.env?.acceptFileExtensionTemplate || this.config.acceptFileExtensionTemplate;
  blankVal = '';
  listFile = [];
  removedFiles = [];
  checkValue: Boolean = false;
  updateForm = new FormGroup({
    name: new FormControl(''),
    subsystem: new FormControl(''),
    templateType: new FormControl(''),
    listVariable: new FormControl(''),
    code: new FormControl(''),
    signEnable: new FormControl(''),
  });
  // tslint:disable-next-line: max-line-length
  name = new FormControl('', [Validators.required, Validators.pattern(this.config.regValidators), Validators.minLength(5), Validators.maxLength(255)]);
  subsystem = new FormControl('', [Validators.required, Validators.pattern(this.config.regValidators)]);
  templateType = new FormControl('', [Validators.required, Validators.pattern(this.config.regValidators)]);

  templateId: string;
  listSubsystem = [];

  // Search TemplateType
  keywordTemplateType = '';
  totalPagesTemplateType = 0;
  currentPageTemplateType = 0;
  pageSizeTemplateType = 10;
  timeOutTemplateType: any = null;
  listTemplateType: Array<any> = [];

  detailTemplateType = {
    id: '',
    code: '',
    name: []
  };
  response: any = {};
  listProcedureConfigId = [];
  listProcedureConfigIdMulti = [];
  listAgencyConfigId = [];
  listAgencyConfigIdMulti = [];
  listDefaultConfigId = [];
  listDefaultConfigIdMulti = [];
  turnOffBasePad: any = false;

  @ViewChild(JsonEditorComponent) editor: JsonEditorComponent;
  options = new JsonEditorOptions();
  dataJson = [];
  check = true;

  constructor(
    public dialogRef: MatDialogRef<UpdateTemplateComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmUpdateDialogModel,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private snackbarService: SnackbarService,
    private templateService: TemplateService,
  ) {
    this.templateId = data.id;
    this.options.mode = 'code';
    this.options.modes = ['code', 'view'];
    this.options.mainMenuBar = false;
    this.options.statusBar = true;
    this.options.onChange = () => {
      try {
        // console.log(this.editor.get());
      }
      catch (err) {
        // console.log('JSON is invalid');
      }
    };
  }

  ngOnInit(): void {
    this.checkISO();

    this.getDetailTemplate(this.templateId);
    this.getListSubsystem();
    this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
    
    if(!this.turnOffBasePad) {
      this.getListProcedureConfig(this.templateId);
      this.getListAgencyConfig(this.templateId);
      this.getListDefaultConfig();
    }
  }

  // Search Function
  getNextBatch(type) {
    switch (type) {
      case 'templateType': {
        this.currentPageTemplateType += 1;
        this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
        break;
      }
    }
  }

  onEnter(type, event) {
    switch (type) {
      case 'templateType': {
        clearTimeout(this.timeOutTemplateType);
        this.timeOutTemplateType = setTimeout(async () => {
          this.keywordTemplateType = event.target.value;
          this.currentPageTemplateType = 0;
          this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
        }, 300);
        break;
      }
    }
  }

  async resetSearchForm(type) {
    switch (type) {
      case 'templateType': {
        this.currentPageTemplateType = 0;
        this.keywordTemplateType = '';
        this.getListTemplateType(this.keywordTemplateType, this.currentPageTemplateType, this.pageSizeTemplateType);
        break;
      }
    }
  }

  getDetailTemplate(id) {
    this.templateService.getDetailTemplate(id).subscribe(data => {
      this.setViewData(data);
    });
  }

  setViewData(data) {
    const detailSubsystems: Array<string> = [];
    if (data.subsystem !== null && data.subsystem.length > 0) {
      // tslint:disable-next-line: prefer-for-of
      for (let i = 0; i < data.subsystem.length; i++) {
        detailSubsystems.push(data.subsystem[i].id);
      }
    }

    const templateTypeObj = {
      id: data.type.id,
      code: data.type.code,
      name: data.type.name.find(item => item.languageId === this.selectedLangId).name
    };

    this.listTemplateType.unshift(templateTypeObj);

    this.updateForm.patchValue(data);
    this.name.setValue(data.name);
    this.updateForm.patchValue({
      subsystem: detailSubsystems,
      templateType: data.type.id,
      listVariable: data.listVariableString
    });
    if (!!data?.signEnable && data?.signEnable === 1) {
      this.checkValue = true;
    }

    if (data.listVariable !== null) {
      this.dataJson = data.listVariable;
    }
    const file = {
      name: data.file.filename,
      size: data.file.size,
      path: data.file.path
    };
    this.listFile.push(file);
  }

  onDismiss(): void {
    this.dialogRef.close();
  }
  checkCheckBoxvalue(event){
    if (event.checked) {
      this.updateForm.get('signEnable').setValue(1);
    }else{
      this.updateForm.get('signEnable').setValue(0);
    }
  }
  onFileSelected(event) {
    if (event.target.files.length > 0) {
      // tslint:disable-next-line: max-line-length
      if ((this.listAcceptExt.filter(type => type.toLowerCase() === ('.' + event.target.files[0].name.split('.').pop().toLowerCase()))).length < 1) {
        const msgObj = {
          vi: 'Không hỗ trợ dạng tệp tin này, vui lòng chọn loại tệp tin khác!',
          en: 'This file type is not supported, please choose another file type!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      } else {
        for (const i of event.target.files) {
          this.listFile.push(i);
        }
        event.target.value = null;
      }
    }
  }

  removeAttachItem(path: string) {
    if (path === undefined) {
      this.listFile.splice(0, 1);
    } else {
      this.removedFiles.push(path);
      this.listFile.splice(0, 1);
    }
  }

  bytesToSize(size) {
    if (isNaN(parseFloat(size)) || !isFinite(size)) { return '?'; }
    let unit = 0;
    while (size >= 1024) {
      size /= 1024;
      unit++;
    }
    return size.toFixed(+ 0) + ' ' + this.config.fileUnits[unit];
  }

  deleteFile(path) {
    const pathParam = '?path=' + path;
    this.templateService.deleteFile(pathParam).subscribe(result => {
    });
  }

  async save() {
    const updateFormObj = this.updateForm.getRawValue();
    // tslint:disable-next-line: max-line-length
    if (!this.name.errors?.required && !this.name.errors?.minlength && !this.name.errors?.maxlength && updateFormObj.templateType.length !== 0 && !this.updateForm.controls.subsystem.invalid) {
      let check = true;
      try {
        updateFormObj.listVariable = JSON.stringify(this.editor.get());
      }
      catch (err) {
        check = false;
      }
      this.check = check;

      if (!check) {
        const msgObj = {
          vi: 'Chuỗi Array không đúng định dạng!',
          en: 'Array string is malformed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        return;
      }else {
        const jsonString = JSON.stringify(this.editor.get());
        const jsonLength = jsonString.length;
        if (jsonString.substring(0, 1) === '[' && jsonString.substring(jsonLength - 1, jsonLength) === ']') {
        }else{
          const msgObj = {
            vi: 'Chuỗi Array không đúng định dạng!',
            en: 'Array string is malformed!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          return;
        }
      }

      if (this.listFile.length === 0) {
        const msgObj = {
          vi: 'Vui lòng tải lên tệp tin!',
          en: 'Please upload file!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      } else {
        const formObj = this.updateForm.getRawValue();
        const detailSubsystems: Array<any> = [];
        if (formObj.subsystem != null) {
          // tslint:disable-next-line: prefer-for-of
          for (let i = 0; i < formObj.subsystem.length; i++) {
            // tslint:disable-next-line: prefer-for-of
            for (let j = 0; j < this.config.listSubsystem.length; j++) {
              if (formObj.subsystem[i] === this.config.listSubsystem[j].id) {
                detailSubsystems.push(this.config.listSubsystem[j]);
              }
            }
          }
        }

        await this.getDetailTemplateType(formObj.templateType);
        const dataPost = {
          type: {
            id: this.detailTemplateType.id,
            code: this.detailTemplateType.code,
            name: this.detailTemplateType.name
          },
          name: '',
          subsystem: [],
          listVariableString: '[]',
          file: {},
          code: null,
          signEnable: 0
        };
        dataPost.name = this.name.value.trim();
        if (formObj.code) {
          dataPost.code = formObj.code;
        }
        if (formObj.signEnable) {
          dataPost.signEnable = formObj.signEnable;
        }
        dataPost.subsystem = detailSubsystems;
        dataPost.listVariableString = JSON.stringify(this.editor.get());
        if (this.listFile[0].path === undefined) {
          this.deleteFile(this.removedFiles[0]);
          const formData = new FormData();
          formData.append('file', this.listFile[0]);

          this.templateService.fileUpload(formData).subscribe(response => {
            dataPost.file = response;
            this.updateTemplate(this.templateId, dataPost);
          });
        } else {
          const file = {
            filename: this.listFile[0].name,
            size: this.listFile[0].size,
            path: this.listFile[0].path,
          };
          dataPost.file = file;
          this.updateTemplate(this.templateId, dataPost);
        }
      }
    } else {
      const msgObj = {
        vi: 'Vui lòng điền đầy đủ thông tin!',
        en: 'Please fill full information!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }
  }

  updateTemplate(id, dataPost) {
    const requestBody = JSON.stringify(dataPost, null, 2);
    this.templateService.updateTemplate(id, requestBody).subscribe(data => {
      let result;
      if (data.affectedRows > 0) {
        result = {
          name: dataPost.name,
          status: true
        };
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < this.listProcedureConfigId.length; i++) {
          this.updateProcedureConfig(this.templateId, this.listProcedureConfigId[i]);
        }
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < this.listProcedureConfigIdMulti.length; i++) {
          this.updateProcedureConfigMulti(this.listProcedureConfigIdMulti[i].idTemplate, this.listProcedureConfigIdMulti[i].idConfig);
        }
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < this.listAgencyConfigId.length; i++) {
          this.updateAgencyConfig(this.templateId, this.listAgencyConfigId[i]);
        }
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < this.listAgencyConfigIdMulti.length; i++) {
          this.updateAgencyConfigMulti(this.listAgencyConfigIdMulti[i].idTemplate, this.listAgencyConfigIdMulti[i].idConfig);
        }
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < this.listDefaultConfigId.length; i++) {
          this.updateDefaultConfig(this.templateId, this.listDefaultConfigId[i]);
        }
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < this.listDefaultConfigIdMulti.length; i++) {
          this.updateDefaultConfigMulti(this.listDefaultConfigIdMulti[i].idTemplate, this.listDefaultConfigIdMulti[i].idConfig);
        }
      }
      else {
        result = {
          name: dataPost.name,
          status: false
        };
      }
      this.dialogRef.close(result);
    }, err => {
      if (err.error.code === 10002) {
        this.snackbarService.openSnackBar(
          0,
          this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'put'),
          $localize`:@@templateFileNotFound:a`,
          'error_notification',
          this.config.expiredTime
        );
      } else {
        const result = {
          name: dataPost.name,
          status: false
        };
        this.dialogRef.close(result);
      }
    });
  }

  async getDetailTemplateType(id) {
    return new Promise(resolve => {
      const transArr: any = [];
      this.templateService.getDetailCategory(id).subscribe(data => {
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < data.trans.length; i++) {
          const nameObj: any = {};
          nameObj.languageId = data.trans[i].languageId;
          nameObj.name = data.trans[i].name;
          transArr.push(nameObj);
        }
        this.detailTemplateType.id = data.id;
        this.detailTemplateType.code = data.code;
        this.detailTemplateType.name = transArr;

        resolve(true);
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  getLanguageId(selectedLang) {
    if (selectedLang === 'vi') {
      return 228;
    } else {
      return 46;
    }
  }

  getListSubsystem() {
    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < this.config.listSubsystem.length; i++) {
      const subsystem = {
        id: this.config.listSubsystem[i].id,
        code: this.config.listSubsystem[i].code,
        name: ''
      };
      // tslint:disable-next-line: prefer-for-of
      for (let j = 0; j < this.config.listSubsystem[i].name.length; j++) {
        if (this.config.listSubsystem[i].name[j].languageId === this.getLanguageId(this.selectedLang)) {
          subsystem.name = this.config.listSubsystem[i].name[j].name;
        }
      }
      this.listSubsystem.push(subsystem);
    }
  }

  getListTemplateType(keyword, page, size) {
    const searchString = '--by-category-id?category-id=5f5b27924e1bd312a6f3ae1e&keyword=' + keyword + '&page=' + page + '&size=' + size + '&spec=page&sort=order';

    this.templateService.getListCategory(searchString).subscribe(res => {
      const index = res.content.findIndex(item => item.id === this.listTemplateType[0]?.id);
      if (index > -1) {
        res.content.splice(index, 1);
      }

      if (page === 0) {
        if (this.listTemplateType.length === 1) {
          this.listTemplateType = this.listTemplateType.concat(res.content);
        }else {
          this.listTemplateType = res.content;
        }
      } else {
        this.listTemplateType = this.listTemplateType.concat(res.content);
      }
      this.totalPagesTemplateType = res.totalPages;
    }, err => {
      console.log(err);
    });
  }

  async getListProcedureConfig(id) {
    const listProcedureConfigIdMultiObj: any = {};
    const listProcedureConfigIdMultiArr = [];
    const totalPagesObj = await this.templateService.getTotalPagesProcedureConfig(id);

    for (let k = 0; k < totalPagesObj.totalPages; k++) {
      const searchString = '&page=' + k;
      this.templateService.getListProcedureConfig(id, searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          if (data.content[i].template.length > 1) {
            // tslint:disable-next-line: prefer-for-of
            for (let j = 0; j < data.content[i].template.length; j++) {
              listProcedureConfigIdMultiArr.push(data.content[i].template[j].id);
            }
            listProcedureConfigIdMultiObj.idConfig = data.content[i].id;
            listProcedureConfigIdMultiObj.idTemplate = listProcedureConfigIdMultiArr;
            this.listProcedureConfigIdMulti.push(listProcedureConfigIdMultiObj);
          } else {
            this.listProcedureConfigId.push(data.content[i].id);
          }
        }
      }, err => {
        console.log(err);
      });
    }
  }

  updateProcedureConfig(idTemplate, idConfig) {
    const templateObj: any = {};
    const templateArr = [];
    let detailTemplate = {};
    this.templateService.getDetailTemplate(idTemplate).subscribe(dataTemplate => {
      detailTemplate = dataTemplate;
      templateArr.push(detailTemplate);
      templateObj.template = templateArr;
      this.templateService.updateProcedureConfig(idConfig, templateObj).subscribe(data => {
        if (data.affectedRows > 0) {
          console.log('đã cập nhật');
        }
        else {
          console.log('chưa cập nhật');
        }
      }, err => {
        console.log(err);
      });
    });
  }

  async updateProcedureConfigMulti(arrIdTemplate, idConfig) {
    const templateObj: any = {};
    const templateArr = [];
    let detailTemplate = {};

    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < arrIdTemplate.length; i++) {
      detailTemplate = await this.templateService.getDetailTemplatePromise(arrIdTemplate[i]);
      templateArr.push(detailTemplate);
    }
    templateObj.template = templateArr;
    this.templateService.updateProcedureConfig(idConfig, templateObj).subscribe(data => {
      if (data.affectedRows > 0) {
        console.log('đã cập nhật');
      }
      else {
        console.log('chưa cập nhật');
      }
    }, err => {
      console.log(err);
    });
  }

  async getListAgencyConfig(id) {
    const listAgencyConfigIdMultiObj: any = {};
    const listAgencyConfigIdMultiArr = [];
    const totalPagesObj = await this.templateService.getTotalPagesAgencyConfig(id);

    for (let k = 0; k < totalPagesObj.totalPages; k++) {
      const searchString = '&page=' + k;
      this.templateService.getListAgencyConfig(id, searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          if (data.content[i].template.length > 1) {
            // tslint:disable-next-line: prefer-for-of
            for (let j = 0; j < data.content[i].template.length; j++) {
              listAgencyConfigIdMultiArr.push(data.content[i].template[j].id);
            }
            listAgencyConfigIdMultiObj.idConfig = data.content[i].id;
            listAgencyConfigIdMultiObj.idTemplate = listAgencyConfigIdMultiArr;
            this.listAgencyConfigIdMulti.push(listAgencyConfigIdMultiObj);
          } else {
            this.listAgencyConfigId.push(data.content[i].id);
          }
        }
      }, err => {
        console.log(err);
      });
    }
  }

  updateAgencyConfig(idTemplate, idConfig) {
    const templateObj: any = {};
    const templateArr = [];
    let detailTemplate = {};
    this.templateService.getDetailTemplate(idTemplate).subscribe(dataTemplate => {
      detailTemplate = dataTemplate;
      templateArr.push(detailTemplate);
      templateObj.template = templateArr;
      this.templateService.updateAgencyConfig(idConfig, templateObj).subscribe(data => {
        if (data.affectedRows > 0) {
          console.log('đã cập nhật');
        }
        else {
          console.log('chưa cập nhật');
        }
      }, err => {
        console.log(err);
      });
    });
  }

  async updateAgencyConfigMulti(arrIdTemplate, idConfig) {
    const templateObj: any = {};
    const templateArr = [];
    let detailTemplate = {};

    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < arrIdTemplate.length; i++) {
      detailTemplate = await this.templateService.getDetailTemplatePromise(arrIdTemplate[i]);
      templateArr.push(detailTemplate);
    }
    templateObj.template = templateArr;
    this.templateService.updateAgencyConfig(idConfig, templateObj).subscribe(data => {
      if (data.affectedRows > 0) {
        console.log('đã cập nhật');
      }
      else {
        console.log('chưa cập nhật');
      }
    }, err => {
      console.log(err);
    });
  }

  getListDefaultConfig() {
    const listDefaultConfigIdMultiObj: any = {};
    const listDefaultConfigIdMultiArr = [];

    this.templateService.getListDefaultConfig().subscribe(data => {
      for (let i = 0; i < data.numberOfElements; i++) {
        if (data.content[i].template.length > 1) {
          // tslint:disable-next-line: prefer-for-of
          for (let j = 0; j < data.content[i].template.length; j++) {
            listDefaultConfigIdMultiArr.push(data.content[i].template[j].id);
          }
          listDefaultConfigIdMultiObj.idConfig = data.content[i].id;
          listDefaultConfigIdMultiObj.idTemplate = listDefaultConfigIdMultiArr;
          this.listDefaultConfigIdMulti.push(listDefaultConfigIdMultiObj);
        } else {
          this.listDefaultConfigId.push(data.content[i].id);
        }
      }
    }, err => {
      console.log(err);
    });
  }

  updateDefaultConfig(idTemplate, idConfig) {
    const templateObj: any = {};
    const templateArr = [];
    let detailTemplate = {};
    this.templateService.getDetailTemplate(idTemplate).subscribe(dataTemplate => {
      detailTemplate = dataTemplate;
      templateArr.push(detailTemplate);
      templateObj.template = templateArr;
      this.templateService.updateDefaultConfig(idConfig, templateObj).subscribe(data => {
        if (data.affectedRows > 0) {
          console.log('đã cập nhật');
        }
        else {
          console.log('chưa cập nhật');
        }
      }, err => {
        console.log(err);
      });
    });
  }

  async updateDefaultConfigMulti(arrIdTemplate, idConfig) {
    const templateObj: any = {};
    const templateArr = [];
    let detailTemplate = {};

    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < arrIdTemplate.length; i++) {
      detailTemplate = await this.templateService.getDetailTemplatePromise(arrIdTemplate[i]);
      templateArr.push(detailTemplate);
    }
    templateObj.template = templateArr;
    this.templateService.updateDefaultConfig(idConfig, templateObj).subscribe(data => {
      if (data.affectedRows > 0) {
        console.log('đã cập nhật');
      }
      else {
        console.log('chưa cập nhật');
      }
    }, err => {
      console.log(err);
    });
  }

  checkISO(){
    if(!!this.env?.isISO && this.env.isISO?.enable){
      this.turnOffBasePad = this.env.isISO?.turnOffBasePad
    }
  }

}

export class ConfirmUpdateDialogModel {
  constructor(public id: string) {
  }
}
