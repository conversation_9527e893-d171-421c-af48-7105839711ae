<h2><PERSON><PERSON> m<PERSON>c l<PERSON>nh vực</h2>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_searchbar" fxFlex="grow">
        <form class="searchForm" [formGroup]="searchForm">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
                <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex='20'>
                    <mat-label >Từ ngày</mat-label>
                    <input matInput formControlName="fromDate" type="date" value="{{fromDate}}" [(ngModel)]="fromDate">
                </mat-form-field>
                <div fxFlex='1'></div>
                <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex='20'>
                    <mat-label >Đến ngày</mat-label>
                    <input matInput formControlName="toDate" type="date" value="{{toDate}}" [(ngModel)]="toDate">
                </mat-form-field>
                <div fxFlex='1'></div>
                <mat-form-field appearance="outline" fxFlex.gt-sm="30" fxFlex='30' change="changeAgency($event)">
                    <mat-label >Cơ quan</mat-label>
                    <mat-select value={{agencyVal}}  formControlName="agency">
                        <mat-option *ngFor='let a of agency' value="{{a.id}}" >
                            {{ a.name }}</mat-option>
                    </mat-select>
                </mat-form-field>
                <div fxFlex='1'></div>
                <button mat-flat-button fxFlex.gt-sm="12" fxFlex='12' class="downloadExcel" (click)="importDataExcel()">
                    <mat-icon>cloud_download</mat-icon> <span > Xuất excel</span>
                </button>
                <div fxFlex='1'></div>
                <button mat-flat-button fxFlex.gt-sm="12" fxFlex='12' class="searchBtn" (click)="searchBtn()">
                    <mat-icon>bar_chart</mat-icon> <span > Thống kê</span>
                </button>
            </div>
        </form>
    </div>
</div>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <mat-tab-group mat-align-tabs="start" animationDuration="10ms" #tabGroup (selectedTabChange)="getTabLoaded($event)">
            <!-- tab 1 -->
            <mat-tab label="{{label01}}">
                <ng-template matTabContent>
                <div class="frm_tbl">
                    <table mat-table [dataSource]="dataSource">
                        <!-- Header row once group -->
                        <ng-container matColumnDef="No1">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="3">STT</th>
                        </ng-container>
                        <ng-container  matColumnDef="No2" fxFlex="10">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="3"> Lĩnh vực </th>
                        </ng-container>
                        <ng-container matColumnDef="No3" fxFlex="24">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="4"> Số hồ sơ nhận giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="No4" fxFlex="36">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="6"> Kết quả giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="No5"  fxFlex="24">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="4"> Số hồ sơ giải quyết theo cơ chế một cửa </th>
                        </ng-container>
                        <!-- Header row second group -->
                        <ng-container matColumnDef="Num1">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2"> Tổng số </th>
                        </ng-container>
                        <ng-container  matColumnDef="Num2">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3"> Trong đó </th>
                        </ng-container>
                        <ng-container matColumnDef="Num3">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3"> Số hồ sơ đã giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="Num4">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3"> Số hồ sơ đang giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="Num5">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2"> Tổng số </th>
                        </ng-container>
                        <ng-container matColumnDef="Num6">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="2"> Đã giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="Num7">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2"> Đang giải quyết </th>
                        </ng-container>
                        
                        <!-- Header row four group -->
                        <ng-container matColumnDef="N1">
                            <th mat-header-cell *matHeaderCellDef>(1)</th>
                        </ng-container>
                        <ng-container matColumnDef="N2">
                            <th mat-header-cell *matHeaderCellDef>(2)</th>
                        </ng-container>
                        <ng-container matColumnDef="N3">
                            <th mat-header-cell *matHeaderCellDef>(3)</th>
                        </ng-container>
                        <ng-container matColumnDef="N4">
                            <th mat-header-cell *matHeaderCellDef>(4)</th>
                        </ng-container>
                        <ng-container matColumnDef="N5">
                            <th mat-header-cell *matHeaderCellDef>(5)</th>
                        </ng-container>
                        <ng-container matColumnDef="N6">
                            <th mat-header-cell *matHeaderCellDef>(6)</th>
                        </ng-container>
                        <ng-container matColumnDef="N7">
                            <th mat-header-cell *matHeaderCellDef>(7)</th>
                        </ng-container>
                        <ng-container matColumnDef="N8">
                            <th mat-header-cell *matHeaderCellDef>(8)</th>
                        </ng-container>
                        <ng-container matColumnDef="N9">
                            <th mat-header-cell *matHeaderCellDef>(9)</th>
                        </ng-container>
                        <ng-container matColumnDef="N10">
                            <th mat-header-cell *matHeaderCellDef>(10)</th>
                        </ng-container>
                        <ng-container matColumnDef="N11">
                            <th mat-header-cell *matHeaderCellDef>(11)</th>
                        </ng-container>
                        <ng-container matColumnDef="N12">
                            <th mat-header-cell *matHeaderCellDef>(12)</th>
                        </ng-container>
                        <ng-container matColumnDef="N13">
                            <th mat-header-cell *matHeaderCellDef>(13)</th>
                        </ng-container>
                        <ng-container matColumnDef="N14">
                            <th mat-header-cell *matHeaderCellDef>(14)</th>
                        </ng-container>
                        <ng-container matColumnDef="N15">
                            <th mat-header-cell *matHeaderCellDef>(15)</th>
                        </ng-container>
                        <ng-container matColumnDef="N16">
                            <th mat-header-cell *matHeaderCellDef>(16)</th>
                        </ng-container>
                        <!-- Header row third group -->
                        <ng-container matColumnDef="M1">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">STT</th>
                        </ng-container>
                        <ng-container matColumnDef="M2">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Lĩnh vực</th>
                        </ng-container>
                        <ng-container matColumnDef="M3">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M4">
                            <th mat-header-cell *matHeaderCellDef>Số tiếp nhận trực tuyến</th>
                        </ng-container>
                        <ng-container matColumnDef="M5">
                            <th mat-header-cell *matHeaderCellDef>Số kỳ trước chuyển qua</th>
                        </ng-container>
                        <ng-container matColumnDef="M6">
                            <th mat-header-cell *matHeaderCellDef>Số mới tiếp nhận (trực tiếp hoặc dịch vụ bưu chính)</th>
                        </ng-container>
                        <ng-container matColumnDef="M7">
                            <th mat-header-cell *matHeaderCellDef>Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M8">
                            <th mat-header-cell *matHeaderCellDef>Trả đúng thời hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M9">
                            <th mat-header-cell *matHeaderCellDef>Trả quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M10">
                            <th mat-header-cell *matHeaderCellDef>Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M11">
                            <th mat-header-cell *matHeaderCellDef>Chưa đến hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M12">
                            <th mat-header-cell *matHeaderCellDef>Quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M13">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M14">
                            <th mat-header-cell *matHeaderCellDef>Đúng thời hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M15">
                            <th mat-header-cell *matHeaderCellDef>Quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M16">
                            <th mat-header-cell *matHeaderCellDef  [ngStyle]="{'display': 'none'}">Đang giải quyết</th>
                        </ng-container>
                        <!-- Header row third group -->
                        <ng-container matColumnDef="stt">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">STT</th>
                            <td mat-cell *matCellDef="let row" data-label="STT"> {{row.stt}} </td>
                        </ng-container>
                        <ng-container matColumnDef="sector">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Lĩnh vực</th>
                            <td mat-cell *matCellDef="let row" data-label="Lĩnh vực"> {{row.sector}} </td>
                        </ng-container>
                        <ng-container matColumnDef="sum1">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                            <td mat-cell *matCellDef="let row" data-label="Tổng số"> {{row.received}} </td>
                        </ng-container>
                        <ng-container matColumnDef="acceptedOnl">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Số tiếp nhận trực tuyến</th>
                            <td mat-cell *matCellDef="let row" data-label="Số tiếp nhận trực tuyến"> {{row.receivedOnline}} </td>
                        </ng-container>
                        <ng-container matColumnDef="pastDossier">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Số kỳ trước chuyển qua</th>
                            <td mat-cell *matCellDef="let row" data-label="Số kỳ trước chuyển qua"> {{row.receivedOld}} </td>
                        </ng-container>
                        <ng-container matColumnDef="acceptedDirect">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Số mới tiếp nhận (trực tiếp hoặc dịch vụ bưu chính)</th>
                            <td mat-cell *matCellDef="let row" data-label="Số mới tiếp nhận (trực tiếp hoặc dịch vụ bưu chính)"> {{row.receivedTT}} </td>
                        </ng-container>
                        <ng-container matColumnDef="sum2">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                            <td mat-cell *matCellDef="let row" data-label="Tổng số"> {{row.resolved}} </td>
                        </ng-container>
                        <ng-container matColumnDef="returnedOnTime">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Trả đúng thời hạn</th>
                            <td mat-cell *matCellDef="let row" data-label="Trả đúng thời hạn"> {{row.resolvedEarly}} </td>
                        </ng-container>
                        <ng-container matColumnDef="returnedOvertime">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Trả quá hạn</th>
                            <td mat-cell *matCellDef="let row" data-label="Trả quá hạn"> {{row.resolvedOverdue}} </td>
                        </ng-container>
                        <ng-container matColumnDef="sum3">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                            <td mat-cell *matCellDef="let row" data-label="Tổng số"> {{row.unresolved}} </td>
                        </ng-container>
                        <ng-container matColumnDef="unresolvedNoTime">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Chưa đến hạn</th>
                            <td mat-cell *matCellDef="let row" data-label="Chưa đến hạn"> {{row.unresolvedHadTime}} </td>
                        </ng-container>
                        <ng-container matColumnDef="unresolvedOvertime">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Quá hạn</th>
                            <td mat-cell *matCellDef="let row" data-label="Quá hạn"> {{row.unresolvedOverdue}} </td>
                        </ng-container>
                        <ng-container matColumnDef="sum4">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                            <td mat-cell *matCellDef="let row" data-label="Tổng số"> {{row.total}} </td>
                        </ng-container>
                        <ng-container matColumnDef="resolvedOnTime">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Đúng thời hạn</th>
                            <td mat-cell *matCellDef="let row" data-label="Đúng thời hạn"> {{row.resolvedEarly}} </td>
                        </ng-container>
                        <ng-container matColumnDef="resolvedOvertime">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Quá hạn</th>
                            <td mat-cell *matCellDef="let row" data-label="Quá hạn"> {{row.resolvedOverdue}} </td>
                        </ng-container>
                        <ng-container matColumnDef="unresolved">
                            <th mat-header-cell *matHeaderCellDef  [ngStyle]="{'display': 'none'}">Đang giải quyết</th>
                            <td mat-cell *matCellDef="let row" data-label="Đang giải quyết"> {{row.unresolved}} </td>
                        </ng-container>

                        <tr mat-header-row *matHeaderRowDef="['No1', 'No2', 'No3', 'No4', 'No5']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['Num1', 'Num2', 'Num3', 'Num4', 'Num5', 'Num6', 'Num7']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7', 'M8', 'M9', 'M10', 'M11', 'M12', 'M13', 'M14', 'M15', 'M16']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['N1', 'N2', 'N3', 'N4', 'N5', 'N6', 'N7', 'N8', 'N9', 'N10', 'N11', 'N12', 'N13', 'N14', 'N15', 'N16']"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                        <!-- <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr> -->
                    </table>
                    <div class="frm_Pagination">
                        <ul class="temp_Arr">
                            <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}"></li>
                        </ul>
                        <div class="pageSize">
                            <span >Hiển thị </span>
                            <mat-form-field appearance="outline">
                                <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                                    <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                            <span >trên </span> {{countResult}} <span >bản ghi</span>
                        </div>
                        <div class="control">
                            <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true"
                                previousLabel="" nextLabel="">
                            </pagination-controls>
                        </div>
                    </div>  
                </div>              
              </ng-template>
            </mat-tab>
            <!-- tab 2 -->
            <mat-tab label="{{label02}}">
                <ng-template matTabContent>
                <div class="frm_tbl1">
                    <table mat-table #outerSort="matSort" multiTemplateDataRows matSort [dataSource]="dataSource1">
                        <!-- Header row once group -->
                        <ng-container matColumnDef="No11">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="3">STT</th>
                        </ng-container>
                        <ng-container  matColumnDef="No12" fxFlex="10">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="3"> Lĩnh vực </th>
                        </ng-container>
                        <ng-container matColumnDef="No13" fxFlex="24">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="4"> Số hồ sơ nhận giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="No14" fxFlex="36">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="6"> Kết quả giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="No15"  fxFlex="24">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="4"> Số hồ sơ giải quyết theo cơ chế một cửa </th>
                        </ng-container>

                        <!-- Header row second group -->
                        <ng-container matColumnDef="Num11">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2"> Tổng số </th>
                        </ng-container>
                        <ng-container  matColumnDef="Num12">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3"> Trong đó </th>
                        </ng-container>
                        <ng-container matColumnDef="Num13">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3"> Số hồ sơ đã giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="Num14">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3"> Số hồ sơ đang giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="Num15">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2"> Tổng số </th>
                        </ng-container>
                        <ng-container matColumnDef="Num16">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="2"> Đã giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="Num17">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2"> Đang giải quyết </th>
                        </ng-container>
                        
                        <!-- Header row third group -->
                        <ng-container matColumnDef="K1">
                            <th mat-header-cell *matHeaderCellDef>(1)</th>
                        </ng-container>
                        <ng-container matColumnDef="K2">
                            <th mat-header-cell *matHeaderCellDef>(2)</th>
                        </ng-container>
                        <ng-container matColumnDef="K3">
                            <th mat-header-cell *matHeaderCellDef>(3)</th>
                        </ng-container>
                        <ng-container matColumnDef="K4">
                            <th mat-header-cell *matHeaderCellDef>(4)</th>
                        </ng-container>
                        <ng-container matColumnDef="K5">
                            <th mat-header-cell *matHeaderCellDef>(5)</th>
                        </ng-container>
                        <ng-container matColumnDef="K6">
                            <th mat-header-cell *matHeaderCellDef>(6)</th>
                        </ng-container>
                        <ng-container matColumnDef="K7">
                            <th mat-header-cell *matHeaderCellDef>(7)</th>
                        </ng-container>
                        <ng-container matColumnDef="K8">
                            <th mat-header-cell *matHeaderCellDef>(8)</th>
                        </ng-container>
                        <ng-container matColumnDef="K9">
                            <th mat-header-cell *matHeaderCellDef>(9)</th>
                        </ng-container>
                        <ng-container matColumnDef="K10">
                            <th mat-header-cell *matHeaderCellDef>(10)</th>
                        </ng-container>
                        <ng-container matColumnDef="K11">
                            <th mat-header-cell *matHeaderCellDef>(11)</th>
                        </ng-container>
                        <ng-container matColumnDef="K12">
                            <th mat-header-cell *matHeaderCellDef>(12)</th>
                        </ng-container>
                        <ng-container matColumnDef="K13">
                            <th mat-header-cell *matHeaderCellDef>(13)</th>
                        </ng-container>
                        <ng-container matColumnDef="K14">
                            <th mat-header-cell *matHeaderCellDef>(14)</th>
                        </ng-container>
                        <ng-container matColumnDef="K15">
                            <th mat-header-cell *matHeaderCellDef>(15)</th>
                        </ng-container>
                        <ng-container matColumnDef="K16">
                            <th mat-header-cell *matHeaderCellDef>(16)</th>
                        </ng-container>

                        <!-- Header row four group -->
                        <ng-container matColumnDef="M11">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">STT</th>
                        </ng-container>
                        <ng-container matColumnDef="M12">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Lĩnh vực</th>
                        </ng-container>
                        <ng-container matColumnDef="M13">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M14">
                            <th mat-header-cell *matHeaderCellDef>Số tiếp nhận trực tuyến</th>
                        </ng-container>
                        <ng-container matColumnDef="M15">
                            <th mat-header-cell *matHeaderCellDef>Số kỳ trước chuyển qua</th>
                        </ng-container>
                        <ng-container matColumnDef="M16">
                            <th mat-header-cell *matHeaderCellDef>Số mới tiếp nhận (trực tiếp hoặc dịch vụ bưu chính)</th>
                        </ng-container>
                        <ng-container matColumnDef="M17">
                            <th mat-header-cell *matHeaderCellDef>Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M18">
                            <th mat-header-cell *matHeaderCellDef>Trả đúng thời hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M19">
                            <th mat-header-cell *matHeaderCellDef>Trả quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M110">
                            <th mat-header-cell *matHeaderCellDef>Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M111">
                            <th mat-header-cell *matHeaderCellDef>Chưa đến hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M112">
                            <th mat-header-cell *matHeaderCellDef>Quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M113">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M114">
                            <th mat-header-cell *matHeaderCellDef>Đúng thời hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M115">
                            <th mat-header-cell *matHeaderCellDef>Quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M116">
                            <th mat-header-cell *matHeaderCellDef  [ngStyle]="{'display': 'none'}">Đang giải quyết</th>
                        </ng-container>

                        <ng-container matColumnDef="agencyLevel">
                            <td mat-cell *matCellDef="let row1" data-label="Cấp" [attr.colspan]="displayedColumns1.length"> {{row1.agencyLevel.name}} </td>
                        </ng-container>

                        <ng-container matColumnDef="expandedDetail" class="frm_tab1">
                            <td mat-cell *matCellDef="let val1" [attr.colspan]="displayedColumns1.length">
                                <div class="example-element-detail" *ngIf="val1.value?.data.length" [@detailExpand]="val1 == expandedElement ? 'expanded' : 'collapsed'">
                                <table mat-table [dataSource]="val1.value" #innerTables mat-table #innerSort="matSort" matSort>
                                    <ng-container matColumnDef="stt1">
                                        <td mat-cell *matCellDef="let val1" data-label="STT"> {{val1.stt}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="sector1">
                                        <td mat-cell *matCellDef="let val1" data-label="Lĩnh vực"> {{val1.sector}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="sum11">
                                        <td mat-cell *matCellDef="let val1" data-label="Tổng số"> {{val1.received}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="acceptedOnl1">
                                        <td mat-cell *matCellDef="let val1" data-label="Số tiếp nhận trực tuyến"> {{val1.receivedOnline}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="pastDossier1">
                                        <td mat-cell *matCellDef="let val1" data-label="Số kỳ trước chuyển qua"> {{val1.receivedOld}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="acceptedDirect1">
                                        <td mat-cell *matCellDef="let val1" data-label="Số mới tiếp nhận (trực tiếp hoặc dịch vụ bưu chính)"> {{val1.receivedTT}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="sum12">
                                        <td mat-cell *matCellDef="let val1" data-label="Tổng số"> {{val1.resolved}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="returnedOnTime1">
                                        <td mat-cell *matCellDef="let val1" data-label="Trả đúng thời hạn"> {{val1.resolvedEarly}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="returnedOvertime1">
                                        <td mat-cell *matCellDef="let val1" data-label="Trả quá hạn"> {{val1.resolvedOverdue}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="sum13">
                                        <td mat-cell *matCellDef="let val1" data-label="Tổng số"> {{val1.unresolved}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="unresolvedNoTime1">
                                        <td mat-cell *matCellDef="let val1" data-label="Chưa đến hạn"> {{val1.unresolvedHadTime}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="unresolvedOvertime1">
                                        <td mat-cell *matCellDef="let val1" data-label="Quá hạn"> {{val1.unresolvedOverdue}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="sum14">
                                        <td mat-cell *matCellDef="let val1" data-label="Tổng số k"> {{val1.resolved}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="resolvedOnTime1">
                                        <td mat-cell *matCellDef="let val1" data-label="Đúng thời hạn"> {{val1.resolvedEarly}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="resolvedOvertime1">
                                        <td mat-cell *matCellDef="let val1" data-label="Quá hạn"> {{val1.resolvedOverdue}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="unresolved1">
                                        <td mat-cell *matCellDef="let val1" data-label="Đang giải quyết"> {{val1.unresolved}} </td>
                                    </ng-container>
                                    <tr mat-row *matRowDef="let val1; columns: displayedColumns1;"></tr>
                                </table>
                                </div>
                            </td>
                        </ng-container>
                        
                        <tr mat-header-row *matHeaderRowDef="['No11', 'No12', 'No13', 'No14', 'No15']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['Num11', 'Num12', 'Num13', 'Num14', 'Num15', 'Num16', 'Num17']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['M11', 'M12', 'M13', 'M14', 'M15', 'M16', 'M17', 'M18', 'M19', 'M110', 'M111', 'M112', 'M113', 'M114', 'M115', 'M116']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['K1', 'K2', 'K3', 'K4', 'K5', 'K6', 'K7', 'K8', 'K9', 'K10', 'K11', 'K12', 'K13', 'K14', 'K15', 'K16']"></tr>
                        
                        <tr mat-row *matRowDef="let val1; columns: displayedColumns11;" [class.example-element-row]="val1.value?.data.length"
                        [class.example-expanded-row]="expandedElement === val1" (click)="toggleRow(val1)"></tr>
                        <tr mat-row *matRowDef="let val1; columns: ['expandedDetail']" class="example-detail-row"></tr>
                    </table>
                    <div class="frm_Pagination">
                        <ul class="temp_Arr">
                            <li *ngFor="let item1 of ELEMENTDATA1  | paginate: {itemsPerPage: size1, currentPage: page1, totalItems: countResult1, id: 'pgnx'}"></li>
                        </ul>
                        <div class="pageSize">
                            <span >Hiển thị </span>
                            <mat-form-field appearance="outline">
                                <mat-select [(value)]="size1" (valueChange)="paginate1(pageIndex1, 1)">
                                    <mat-option *ngFor='let opt of pgSizeOptions1;' [value]="opt">{{opt}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                            <span >trên </span> {{countResult1}} <span >bản ghi</span>
                        </div>
                        <div class="control">
                            <pagination-controls id="pgnx" (pageChange)="page = $event; paginate1(page, 0)" responsive="true"
                                previousLabel="" nextLabel="">
                            </pagination-controls>
                        </div>
                    </div>  
                </div>              
              </ng-template>
            </mat-tab>
            <!-- tab 3 -->
            <mat-tab label="{{label03}}">
                <ng-template matTabContent>
                <div class="frm_tbl2">
                    <table mat-table [dataSource]="dataSource2" multiTemplateDataRows #outerSort="matSort" matSort>
                        <!-- Header row once group -->
                        <ng-container matColumnDef="No21">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="3">STT</th>
                        </ng-container>
                        <ng-container  matColumnDef="No22" fxFlex="10">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="3"> Lĩnh vực </th>
                        </ng-container>
                        <ng-container matColumnDef="No23" fxFlex="24">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="4"> Số hồ sơ nhận giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="No24" fxFlex="36">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="6"> Kết quả giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="No25"  fxFlex="24">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="4"> Số hồ sơ giải quyết theo cơ chế một cửa </th>
                        </ng-container>

                        <!-- Header row second group -->
                        <ng-container matColumnDef="Num21">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2"> Tổng số </th>
                        </ng-container>
                        <ng-container  matColumnDef="Num22">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3"> Trong đó </th>
                        </ng-container>
                        <ng-container matColumnDef="Num23">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3"> Số hồ sơ đã giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="Num24">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3"> Số hồ sơ đang giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="Num25">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2"> Tổng số </th>
                        </ng-container>
                        <ng-container matColumnDef="Num26">
                            <th mat-header-cell *matHeaderCellDef [attr.colspan]="2"> Đã giải quyết </th>
                        </ng-container>
                        <ng-container matColumnDef="Num27">
                            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2"> Đang giải quyết </th>
                        </ng-container>
                        
                        <!-- Header row third group -->
                        <ng-container matColumnDef="K11">
                            <th mat-header-cell *matHeaderCellDef>(1)</th>
                        </ng-container>
                        <ng-container matColumnDef="K12">
                            <th mat-header-cell *matHeaderCellDef>(2)</th>
                        </ng-container>
                        <ng-container matColumnDef="K13">
                            <th mat-header-cell *matHeaderCellDef>(3)</th>
                        </ng-container>
                        <ng-container matColumnDef="K14">
                            <th mat-header-cell *matHeaderCellDef>(4)</th>
                        </ng-container>
                        <ng-container matColumnDef="K15">
                            <th mat-header-cell *matHeaderCellDef>(5)</th>
                        </ng-container>
                        <ng-container matColumnDef="K16">
                            <th mat-header-cell *matHeaderCellDef>(6)</th>
                        </ng-container>
                        <ng-container matColumnDef="K17">
                            <th mat-header-cell *matHeaderCellDef>(7)</th>
                        </ng-container>
                        <ng-container matColumnDef="K18">
                            <th mat-header-cell *matHeaderCellDef>(8)</th>
                        </ng-container>
                        <ng-container matColumnDef="K19">
                            <th mat-header-cell *matHeaderCellDef>(9)</th>
                        </ng-container>
                        <ng-container matColumnDef="K110">
                            <th mat-header-cell *matHeaderCellDef>(10)</th>
                        </ng-container>
                        <ng-container matColumnDef="K111">
                            <th mat-header-cell *matHeaderCellDef>(11)</th>
                        </ng-container>
                        <ng-container matColumnDef="K112">
                            <th mat-header-cell *matHeaderCellDef>(12)</th>
                        </ng-container>
                        <ng-container matColumnDef="K113">
                            <th mat-header-cell *matHeaderCellDef>(13)</th>
                        </ng-container>
                        <ng-container matColumnDef="K114">
                            <th mat-header-cell *matHeaderCellDef>(14)</th>
                        </ng-container>
                        <ng-container matColumnDef="K115">
                            <th mat-header-cell *matHeaderCellDef>(15)</th>
                        </ng-container>
                        <ng-container matColumnDef="K116">
                            <th mat-header-cell *matHeaderCellDef>(16)</th>
                        </ng-container>

                        <!-- Header row four group -->
                        <ng-container matColumnDef="M21">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">STT</th>
                        </ng-container>
                        <ng-container matColumnDef="M22">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Lĩnh vực</th>
                        </ng-container>
                        <ng-container matColumnDef="M23">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M24">
                            <th mat-header-cell *matHeaderCellDef>Số tiếp nhận trực tuyến</th>
                        </ng-container>
                        <ng-container matColumnDef="M25">
                            <th mat-header-cell *matHeaderCellDef>Số kỳ trước chuyển qua</th>
                        </ng-container>
                        <ng-container matColumnDef="M26">
                            <th mat-header-cell *matHeaderCellDef>Số mới tiếp nhận (trực tiếp hoặc dịch vụ bưu chính)</th>
                        </ng-container>
                        <ng-container matColumnDef="M27">
                            <th mat-header-cell *matHeaderCellDef>Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M28">
                            <th mat-header-cell *matHeaderCellDef>Trả đúng thời hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M29">
                            <th mat-header-cell *matHeaderCellDef>Trả quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M210">
                            <th mat-header-cell *matHeaderCellDef>Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M211">
                            <th mat-header-cell *matHeaderCellDef>Chưa đến hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M212">
                            <th mat-header-cell *matHeaderCellDef>Quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M213">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Tổng số</th>
                        </ng-container>
                        <ng-container matColumnDef="M214">
                            <th mat-header-cell *matHeaderCellDef>Đúng thời hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M215">
                            <th mat-header-cell *matHeaderCellDef>Quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M216">
                            <th mat-header-cell *matHeaderCellDef  [ngStyle]="{'display': 'none'}">Đang giải quyết</th>
                        </ng-container>

                        <ng-container matColumnDef="agencyLevel2">
                            <td mat-cell *matCellDef="let row2" data-label="Cấp" [attr.colspan]="displayedColumns2.length"> {{row2.agencyLevel.name}} </td>
                        </ng-container>

                        <ng-container matColumnDef="expandedDetail2" class="frm_tab2">
                            <td mat-cell *matCellDef="let val2" [attr.colspan]="displayedColumns2.length">
                                <div class="example-element-detailss" *ngIf="val2.value?.data.length" [@detailExpand]="val2 == expandedElement ? 'expanded' : 'collapsed'">
                                <table mat-table [dataSource]="val2.value.filteredData" multiTemplateDataRows #innerTables mat-table #innerSort="matSort" matSort>
                                    <ng-container matColumnDef="agency">
                                        <td mat-cell *matCellDef="let val2" data-label="Cơ quan" [attr.colspan]="displayedColumns2.length" class="agencytag"> {{val2.agency}} </td>
                                    </ng-container>
                                   <!-- chỗ này -->
                                    <ng-container matColumnDef="expandedDetail22">
                                        <td mat-cell *matCellDef="let val2" [attr.colspan]="displayedColumns2.length">
                                            <table mat-table [dataSource]="val2.value" multiTemplateDataRows>
                                                <ng-container matColumnDef="stt2">
                                                    <td mat-cell *matCellDef="let val22" data-label="STT"> {{val22.stt}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="sector2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Lĩnh vực"> {{val22.sector}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="sum21">
                                                    <td mat-cell *matCellDef="let val22" data-label="Tổng số"> {{val22.received}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="acceptedOnl2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Số tiếp nhận trực tuyến"> {{val22.receivedOnline}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="pastDossier2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Số kỳ trước chuyển qua"> {{val22.receivedOld}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="acceptedDirect2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Số mới tiếp nhận (trực tiếp hoặc dịch vụ bưu chính)"> {{val22.receivedTT}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="sum22">
                                                    <td mat-cell *matCellDef="let val22" data-label="Tổng số"> {{val22.resolved}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="returnedOnTime2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Trả đúng thời hạn"> {{val22.resolvedEarly}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="returnedOvertime2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Trả quá hạn"> {{val22.resolvedOverdue}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="sum23">
                                                    <td mat-cell *matCellDef="let val22" data-label="Tổng số"> {{val22.unresolved}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="unresolvedNoTime2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Chưa đến hạn"> {{val22.unresolvedHadTime}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="unresolvedOvertime2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Quá hạn"> {{val22.unresolvedOverdue}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="sum24">
                                                    <td mat-cell *matCellDef="let val22" data-label="Tổng số k"> {{val22.resolved}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="resolvedOnTime2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Đúng thời hạn"> {{val22.resolvedEarly}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="resolvedOvertime2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Quá hạn"> {{val22.resolvedOverdue}} </td>
                                                </ng-container>
                                                <ng-container matColumnDef="unresolved2">
                                                    <td mat-cell *matCellDef="let val22" data-label="Đang giải quyết"> {{val22.unresolved}} </td>
                                                </ng-container>
                                                <tr mat-row *matRowDef="let val22; columns: displayedColumns2;"></tr>
                                            </table>
                                        </td>
                                    </ng-container>
                                    <!-- end  -->
                                    <tr mat-row *matRowDef="let val2; columns: displayedColumns122;"></tr>
                                    <tr mat-row *matRowDef="let val2; columns: ['expandedDetail22']" class="example-detail-rowsss"></tr>
                                </table>
                                </div>
                            </td>
                        </ng-container>                       

                        <tr mat-header-row *matHeaderRowDef="['No21', 'No22', 'No23', 'No24', 'No25']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['Num21', 'Num22', 'Num23', 'Num24', 'Num25', 'Num26', 'Num27']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['M21', 'M22', 'M23', 'M24', 'M25', 'M26', 'M27', 'M28', 'M29', 'M210', 'M211', 'M212', 'M213', 'M214', 'M215', 'M216']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['K11', 'K12', 'K13', 'K14', 'K15', 'K16', 'K17', 'K18', 'K19', 'K110', 'K111', 'K112', 'K113', 'K114', 'K115', 'K116']"></tr>
                        <tr mat-row *matRowDef="let val2; columns: displayedColumns12;" [class.example-element-rowss]="val2.value?.data.length"
                        [class.example-expanded-rowss]="expandedElement === val2" (click)="toggleRow(val2)"></tr>
                        <tr mat-row *matRowDef="let val2; columns: ['expandedDetail2']" class="example-detail-rowss"></tr>
                    </table>
                    <div class="frm_Pagination">
                        <ul class="temp_Arr">
                            <li *ngFor="let item2 of ELEMENTDATA2  | paginate: {itemsPerPage: size2, currentPage: page2, totalItems: countResult2, id: 'pgnx'}"></li>
                        </ul>
                        <div class="pageSize">
                            <span >Hiển thị </span>
                            <mat-form-field appearance="outline">
                                <mat-select [(value)]="size2" (valueChange)="paginate2(pageIndex1, 1)">
                                    <mat-option *ngFor='let opt of pgSizeOptions2;' [value]="opt">{{opt}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                            <span >trên </span> {{countResult2}} <span >bản ghi</span>
                        </div>
                        <div class="control">
                            <pagination-controls id="pgnx" (pageChange)="page = $event; paginate2(page, 0)" responsive="true"
                                previousLabel="" nextLabel="">
                            </pagination-controls>
                        </div>
                    </div>  
                </div>              
            </ng-template>
            </mat-tab>
            <!-- tab 4 -->
            <mat-tab label="{{label04}}">
                <ng-template matTabContent>
                <div class="frm_tbl3">
                    <table mat-table [dataSource]="dataSource3">
                    	<!-- Header row once group -->
                        <ng-container matColumnDef="M31">
                            <th mat-header-cell *matHeaderCellDef>STT</th>
                        </ng-container>
                        <ng-container matColumnDef="M32">
                            <th mat-header-cell *matHeaderCellDef>Lĩnh vực, công việc giải quyết</th>
                        </ng-container>
                        <ng-container matColumnDef="M33">
                            <th mat-header-cell *matHeaderCellDef>Số lượng hồ sơ</th>
                        </ng-container>
                        <ng-container matColumnDef="M34">
                            <th mat-header-cell *matHeaderCellDef>Nguyên nhân quá hạn</th>
                        </ng-container>
                        <ng-container matColumnDef="M35">
                            <th mat-header-cell *matHeaderCellDef>Ghi chú</th>
                        </ng-container>

                        <!-- Header row second group -->
                        <ng-container matColumnDef="K21">
                            <th mat-header-cell *matHeaderCellDef>(1)</th>
                        </ng-container>
                        <ng-container matColumnDef="K22">
                            <th mat-header-cell *matHeaderCellDef>(2)</th>
                        </ng-container>
                        <ng-container matColumnDef="K23">
                            <th mat-header-cell *matHeaderCellDef>(3)</th>
                        </ng-container>
                        <ng-container matColumnDef="K24">
                            <th mat-header-cell *matHeaderCellDef>(4)</th>
                        </ng-container>
                        <ng-container matColumnDef="K25">
                            <th mat-header-cell *matHeaderCellDef>(5)</th>
                        </ng-container>

                        <!-- Header row third group -->
                        <ng-container matColumnDef="stt3">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">STT</th>
                            <td mat-cell *matCellDef="let row3" data-label="STT"> {{row3.stt}} </td>
                        </ng-container>
                        <ng-container matColumnDef="sector3">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Lĩnh vực</th>
                            <td mat-cell *matCellDef="let row3" data-label="Lĩnh vực, công việc giải quyết"> {{row3.sector}} </td>
                        </ng-container>
                        <ng-container matColumnDef="sum3">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Số lượng hồ sơ</th>
                            <td mat-cell *matCellDef="let row3" data-label="Số lượng hồ sơ"> {{row3.unresolvedOverdue}} </td>
                        </ng-container>
                        <ng-container matColumnDef="cause3">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Nguyên nhân quá hạn</th>
                            <td mat-cell *matCellDef="let row3" data-label="Nguyên nhân quá hạn">  </td>
                        </ng-container>
                        <ng-container matColumnDef="note3">
                            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Ghi chú</th>
                            <td mat-cell *matCellDef="let row3" data-label="Ghi chú">  </td>
                        </ng-container>

                        <tr mat-header-row *matHeaderRowDef="['M31', 'M32', 'M33', 'M34', 'M35']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['K21', 'K22', 'K23', 'K24', 'K25']"></tr>
                        <tr mat-row *matRowDef="let row3; columns: displayedColumns3;"></tr>
                        <!-- <tr mat-header-row *matHeaderRowDef="displayedColumns3"></tr> -->
                    </table>
                    <div class="frm_Pagination">
                        <ul class="temp_Arr">
                            <li *ngFor="let item3 of ELEMENTDATA3  | paginate: {itemsPerPage: size3, currentPage: page3, totalItems: countResult3, id: 'pgnx'}"></li>
                        </ul>
                        <div class="pageSize">
                            <span >Hiển thị </span>
                            <mat-form-field appearance="outline">
                                <mat-select [(value)]="size3" (valueChange)="paginate3(pageIndex1, 1)">
                                    <mat-option *ngFor='let opt of pgSizeOptions3;' [value]="opt">{{opt}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                            <span >trên </span> {{countResult3}} <span >bản ghi</span>
                        </div>
                        <div class="control">
                            <pagination-controls id="pgnx" (pageChange)="page = $event; paginate3(page, 0)" responsive="true"
                                previousLabel="" nextLabel="">
                            </pagination-controls>
                        </div>
                    </div>  
                </div>              
            </ng-template>
            </mat-tab>
            <!-- tab 5 -->
            <mat-tab label="{{label05}}">
                <ng-template matTabContent>
                <div class="frm_tbl4">
                    <table mat-table [dataSource]="dataSource4" multiTemplateDataRows #outerSort="matSort" matSort>
                    	<!-- Header row once group -->
                        <ng-container matColumnDef="M41">
                            <th mat-header-cell *matHeaderCellDef>STT</th>
                        </ng-container>
                        <ng-container matColumnDef="M42">
                            <th mat-header-cell *matHeaderCellDef>Tên TTHC</th>
                        </ng-container>
                        <ng-container matColumnDef="M43">
                            <th mat-header-cell *matHeaderCellDef>Nội dung vướng mắc</th>
                        </ng-container>
                        <ng-container matColumnDef="M44">
                            <th mat-header-cell *matHeaderCellDef>Văn bản QPPL</th>
                        </ng-container>

                        <!-- Header row second group -->
                        <ng-container matColumnDef="K31">
                            <th mat-header-cell *matHeaderCellDef>(1)</th>
                        </ng-container>
                        <ng-container matColumnDef="K32">
                            <th mat-header-cell *matHeaderCellDef>(2)</th>
                        </ng-container>
                        <ng-container matColumnDef="K33">
                            <th mat-header-cell *matHeaderCellDef>(3)</th>
                        </ng-container>
                        <ng-container matColumnDef="K34">
                            <th mat-header-cell *matHeaderCellDef>(4)</th>
                        </ng-container>

                        <ng-container matColumnDef="sectorLevel">
                            <td mat-cell *matCellDef="let row4" data-label="Lĩnh vực" [attr.colspan]="displayedColumns4.length"> {{row4.sector.name}} </td>
                        </ng-container>

                        <!-- Header row third group -->
                        <ng-container matColumnDef="expandedDetail4" class="frm_tab4">
                            <td mat-cell *matCellDef="let val4" [attr.colspan]="displayedColumns4.length">
                                <div class="example-element-details" *ngIf="val4.value?.data.length" [@detailExpand]="val4 == expandedElement ? 'expanded' : 'collapsed'">
                                    <table mat-table [dataSource]="val4.value" #innerTables mat-table #innerSort="matSort" matSort>
                                    <ng-container matColumnDef="stt4">
                                        <td mat-cell *matCellDef="let val4" data-label="STT"> {{val4.stt}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="procedure4">
                                        <td mat-cell *matCellDef="let val4" data-label="Tên TTHC"> {{val4.procedure}}  </td>
                                    </ng-container>
                                    <ng-container matColumnDef="content4">
                                        <td mat-cell *matCellDef="let val4" data-label="Nội dung vướng mắc">  </td>
                                    </ng-container>
                                    <ng-container matColumnDef="file4">
                                        <td mat-cell *matCellDef="let val4" data-label="Văn bản QPPL">  </td>
                                    </ng-container>
                                    <tr mat-row *matRowDef="let val4; columns: displayedColumns4;"></tr>
                                </table>
                                </div>
                            </td>
                        </ng-container>

                        <tr mat-header-row *matHeaderRowDef="['M41', 'M42', 'M43', 'M44']"></tr>
                        <tr mat-header-row *matHeaderRowDef="['K31', 'K32', 'K33', 'K34']"></tr>
                        <tr mat-row *matRowDef="let val4; columns: displayedColumns14;" [class.example-element-rows]="val4.value?.data.length" [class.example-expanded-rows]="expandedElement === val4" (click)="toggleRow(val4)"></tr>
                        <tr mat-row *matRowDef="let val4; columns: ['expandedDetail4']" class="example-detail-rows"></tr>
                    </table>
                    <div class="frm_Pagination">
                        <ul class="temp_Arr">
                            <li *ngFor="let item4 of ELEMENTDATA4  | paginate: {itemsPerPage: size4, currentPage: page4, totalItems: countResult4, id: 'pgnx'}"></li>
                        </ul>
                        <div class="pageSize">
                            <span >Hiển thị </span>
                            <mat-form-field appearance="outline">
                                <mat-select [(value)]="size4" (valueChange)="paginate4(pageIndex4, 1)">
                                    <mat-option *ngFor='let opt of pgSizeOptions4;' [value]="opt">{{opt}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                            <span >trên </span> {{countResult4}} <span >bản ghi</span>
                        </div>
                        <div class="control">
                            <pagination-controls id="pgnx" (pageChange)="page = $event; paginate4(page, 0)" responsive="true"
                                previousLabel="" nextLabel="">
                            </pagination-controls>
                        </div>
                    </div>  
                </div>              
            </ng-template>
            </mat-tab>
        </mat-tab-group>          
    </div>
</div>